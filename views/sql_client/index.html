{% extends "base/harmattan.html" %}

{% block title %}SQL Client - Harmattan{% endblock %}

{% block header %}
<small class="text-muted">Query your data</small>
<h1 class="h4 mt-1">SQL Client</h1>
{% endblock %}

{% block header_actions %}
<button type="button" class="btn btn-success" id="runQueryBtn">
    <i class="fa fa-play me-1"></i> Run Query
</button>
<button type="button" class="btn btn-outline-secondary" id="clearBtn">
    <i class="fa fa-eraser me-1"></i> Clear
</button>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
<style>
.sql-editor {
    height: 50vh;
    border: 1px solid #ddd;
    border-radius: 4px;
}
.CodeMirror {
    height: 100%;
    font-size: 14px;
}
.results-panel {
    height: 45vh;
    overflow: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}
.query-info {
    background: #f8f9fa;
    padding: 8px 12px;
    border-bottom: 1px solid #ddd;
    font-size: 12px;
}
.table-container {
    overflow: auto;
    max-height: calc(45vh - 40px);
}
.results-table {
    font-size: 12px;
}
.results-table th {
    background: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
}
.error-message {
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 12px;
    margin: 12px;
}

/* Custom SQL keyword highlighting */
.CodeMirror .cm-keyword.cm-custom-keyword {
    color: #e91e63 !important;
    font-weight: bold;
    background: rgba(233, 30, 99, 0.1);
    border-radius: 2px;
    padding: 1px 2px;
}

/* Visualization results styling */
.visualization-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 10px;
}

/* Table summary styling for TABULATE results - using same style as SELECT results */

.table-summary-container .gt_table,
.table-summary-container table {
    /* Apply same styling as results-table */
    font-size: 12px !important;
    width: 100%;
    border-collapse: collapse;
}

.table-summary-container .gt_col_headings th,
.table-summary-container .gt_col_heading,
.table-summary-container thead th,
.table-summary-container th {
    background: #f8f9fa !important;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
    padding: 8px 12px !important;
    border-bottom: 2px solid #dee2e6 !important;
    font-size: 12px !important;
}

.table-summary-container .gt_row,
.table-summary-container tbody tr {
    border-bottom: 1px solid #dee2e6;
}

.table-summary-container .gt_row:nth-child(even),
.table-summary-container tbody tr:nth-child(even) {
    background-color: rgba(0,0,0,.05) !important;
}

.table-summary-container .gt_row:hover,
.table-summary-container tbody tr:hover {
    background-color: #f5f5f5 !important;
}

.table-summary-container td,
.table-summary-container .gt_row td,
.table-summary-container tbody td {
    padding: 8px 12px !important;
    font-size: 12px !important;
    border-top: none !important;
}

/* Override any gtsummary default styling */
.table-summary-container .gt_table .gt_heading {
    background: transparent !important;
    border: none !important;
    padding: 10px !important;
    text-align: center;
    font-weight: bold;
}

.table-summary-container .gt_table .gt_footnotes {
    font-size: 11px !important;
    padding: 8px !important;
}

/* Ensure single variable visualizations use 3 columns */
.visualization-container.single-variable {
    grid-template-columns: repeat(3, 1fr);
    min-height: 400px;
}

/* When there are 4 plots (typical single variable), arrange nicely */
.visualization-container.single-variable .visualization-item:nth-child(4) {
    grid-column: 2; /* Center the 4th plot in the second row */
}

.visualization-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.visualization-item h6 {
    margin-bottom: 15px;
    color: #495057;
    font-weight: 600;
}

.visualization-item svg, .visualization-item img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
}

.image-container, .svg-container {
    cursor: pointer;
    transition: transform 0.2s ease;
}

.image-container:hover, .svg-container:hover {
    transform: scale(1.02);
}

/* Responsive design for visualizations */
@media (max-width: 1200px) {
    .visualization-container.single-variable {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .visualization-container {
        grid-template-columns: 1fr;
    }

    .visualization-container.single-variable {
        grid-template-columns: 1fr;
    }
}

/* Multi-column visualization styling */
.plot-type-section {
    margin-bottom: 30px;
}

.plot-type-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
    padding: 10px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    text-align: center;
    margin: 10px;
}

/* Adjust grid for multi-column when there are many plots */
.plot-type-section .visualization-container {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

@media (max-width: 768px) {
    .plot-type-section .visualization-container {
        grid-template-columns: 1fr;
    }
}

/* Bivariate visualization styling */
.visualization-container.bivariate-single {
    grid-template-columns: 1fr;
    justify-items: center;
    max-width: 800px;
    margin: 10px auto;
}

.visualization-container.bivariate-barchart {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

@media (max-width: 768px) {
    .visualization-container.bivariate-barchart {
        grid-template-columns: 1fr;
    }
}
.loading {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- SQL Editor -->
        <div class="card mb-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">SQL Editor</h6>
                <div>
                    <small class="text-muted me-3">Press Ctrl+Enter to run query</small>
                    <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#helpModal">
                        <i class="fa fa-question-circle"></i> Help
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="sql-editor">
                    <textarea id="sqlEditor" placeholder="-- Enter your SQL query here
-- Example: SELECT * FROM your_table_name LIMIT 10;"></textarea>
                </div>
            </div>
        </div>

        <!-- Results Panel -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Query Results</h6>
            </div>
            <div class="card-body p-0">
                <div class="results-panel">
                    <div id="resultsContent" class="text-center py-5 text-muted">
                        <i class="fa fa-code fa-2x mb-3"></i>
                        <p>Run a SQL query to see results here</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">SQL Client Help</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Available Tables:</h6>
                <div class="mb-3">
                    {% if tables %}
                        {% for table in tables %}
                        <span class="badge bg-secondary me-1 mb-1">{{ table.name }}</span>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No tables available. Upload data first.</p>
                    {% endif %}
                </div>
                
                <h6>Example Queries:</h6>
                <div class="mb-3">
                    <pre class="bg-light p-2 rounded"><code>-- View all data from a table
SELECT * FROM table_name LIMIT 100;

-- Count rows
SELECT COUNT(*) FROM table_name;

-- Group by example
SELECT column_name, COUNT(*) 
FROM table_name 
GROUP BY column_name;

-- Join tables
SELECT a.*, b.column_name
FROM table1 a
JOIN table2 b ON a.id = b.id;</code></pre>
                </div>

                <h6>Custom Harmattan SQL Commands:</h6>
                <div class="alert alert-info">
                    <strong>VISUALIZE</strong> - Generate statistical visualizations using R and ggplot2
                </div>
                <pre class="bg-light p-2 rounded"><code>-- Create histogram, density, box and violin plots
VISUALIZE numeric_column FROM table_name;

-- Examples:
VISUALIZE price FROM products;
VISUALIZE age FROM customers;</code></pre>

                <p><strong>VISUALIZE</strong> creates four types of plots:</p>
                <ul>
                    <li><strong>Histogram</strong> - Shows frequency distribution</li>
                    <li><strong>Density Plot</strong> - Shows probability density</li>
                    <li><strong>Box Plot</strong> - Shows quartiles and outliers</li>
                    <li><strong>Violin Plot</strong> - Combines density and box plot</li>
                </ul>

                <div class="alert alert-warning">
                    <strong>Requirements:</strong> R with ggplot2 and dplyr packages must be installed for VISUALIZE commands.
                </div>

                <h6>Custom Harmattan SQL Commands:</h6>
                <div class="alert alert-info">
                    <strong>DESCRIBE</strong> - Show table schema and column information<br>
                    <strong>VISUALIZE</strong> - Generate statistical visualizations using R and ggplot2<br>
                    <small>• <strong>Numeric columns:</strong> Histogram, Density, Box Plot, Violin Plot</small><br>
                    <small>• <strong>Categorical columns:</strong> Bar Chart, Pie Chart, Doughnut Chart</small><br>
                    <small>• <strong>Bivariate plots:</strong> Scatterplot, Grouped Boxplot, Grouped/Stacked Bar Charts</small><br>
                    <strong>TABULATE</strong> - Generate summary statistics tables using R and gtsummary<br>
                    <small>• <strong>Numeric variables:</strong> Median (Q1, Q3)</small><br>
                    <small>• <strong>Categorical variables:</strong> Frequency counts and percentages</small><br>
                    <small>• <strong>Bivariate tables:</strong> Statistics by groups with p-values</small>
                </div>
                <pre class="bg-light p-2 rounded"><code>-- Show table structure and columns
DESCRIBE table_name;

-- Single column visualization
VISUALIZE numeric_column FROM table_name;

-- Multiple columns visualization
VISUALIZE column1, column2, column3 FROM table_name;

-- With WHERE clause (filtering)
VISUALIZE numeric_column FROM table_name WHERE condition = 'value';

-- With LIMIT clause (limit data points)
VISUALIZE numeric_column FROM table_name LIMIT 100;

-- Combined WHERE and LIMIT
VISUALIZE numeric_column FROM table_name WHERE var1 = 'value' AND var2 < 45 LIMIT 100;

-- Multi-column with filters
VISUALIZE Age, Height, Weight FROM nhanes WHERE Gender = 'Female' LIMIT 500;

-- Examples:
DESCRIBE nhanes;

-- Numeric visualizations (histogram, density, box, violin)
VISUALIZE Age FROM nhanes;
VISUALIZE Age, Height FROM nhanes;

-- Categorical visualizations (bar, pie, doughnut)
VISUALIZE Gender FROM nhanes;
VISUALIZE Race, Education FROM nhanes;

-- Mixed numeric and categorical
VISUALIZE Age, Gender FROM nhanes WHERE Age > 18;

-- Bivariate visualizations
VISUALIZE SCATTERPLOT(Age, Height) FROM nhanes;
VISUALIZE BOXPLOT(Age) FROM nhanes GROUP BY Gender;
VISUALIZE BARCHART(Education) FROM nhanes GROUP BY Gender;

-- Summary statistics tables
TABULATE Age, Height, Weight FROM nhanes;
TABULATE Age, Gender FROM nhanes WHERE Age > 18;
TABULATE Age, Height FROM nhanes GROUP BY Gender;
TABULATE Education, Income FROM nhanes WHERE Age > 25 LIMIT 1000;</code></pre>

                <p><strong>VISUALIZE</strong> creates different plots based on data type:</p>

                <p><strong>📊 Numeric Variables:</strong></p>
                <ul>
                    <li><strong>Histogram</strong> - Shows frequency distribution</li>
                    <li><strong>Density Plot</strong> - Shows probability density</li>
                    <li><strong>Box Plot</strong> - Shows quartiles and outliers</li>
                    <li><strong>Violin Plot</strong> - Combines density and box plot</li>
                </ul>

                <p><strong>📈 Categorical Variables:</strong></p>
                <ul>
                    <li><strong>Bar Chart</strong> - Shows frequency count of each category</li>
                    <li><strong>Pie Chart</strong> - Shows proportional distribution</li>
                    <li><strong>Doughnut Chart</strong> - Modern alternative to pie chart</li>
                </ul>

                <p><strong>🔗 Bivariate Visualizations:</strong></p>
                <ul>
                    <li><strong>Scatterplot</strong> - Shows relationship between two numeric variables</li>
                    <li><strong>Grouped Boxplot</strong> - Shows numeric distribution by categorical groups</li>
                    <li><strong>Grouped Bar Chart</strong> - Shows categorical counts by groups</li>
                    <li><strong>Stacked Bar Chart</strong> - Shows categorical percentages by groups</li>
                </ul>

                <p><strong>Advanced VISUALIZE Features:</strong></p>
                <ul>
                    <li><strong>Multiple columns</strong> - Visualize multiple numeric/categorical variables at once</li>
                    <li><strong>Mixed data types</strong> - Combine numeric and categorical columns in one command</li>
                    <li><strong>Bivariate analysis</strong> - Explore relationships between two variables</li>
                    <li><strong>WHERE clause</strong> - Filter data before visualization (e.g., specific gender, age range)</li>
                    <li><strong>LIMIT clause</strong> - Limit number of data points for performance</li>
                    <li><strong>Combined filters</strong> - Use both WHERE and LIMIT together</li>
                    <li><strong>Case-insensitive</strong> - Column names work regardless of case</li>
                    <li><strong>Organized layout</strong> - Multi-column results grouped by plot type</li>
                    <li><strong>Auto-detection</strong> - Automatically detects numeric vs categorical columns</li>
                </ul>

                <p><strong>📋 TABULATE Command:</strong></p>
                <p>The <strong>TABULATE</strong> command generates professional summary statistics tables using the gtsummary R package.</p>

                <p><strong>📊 Numeric Variables:</strong></p>
                <ul>
                    <li><strong>Median (Q1, Q3)</strong> - Shows median with first and third quartiles</li>
                    <li><strong>Robust statistics</strong> - Less sensitive to outliers than mean/SD</li>
                </ul>

                <p><strong>📈 Categorical Variables:</strong></p>
                <ul>
                    <li><strong>Frequency counts</strong> - Number of observations in each category</li>
                    <li><strong>Percentages</strong> - Proportion of total for each category</li>
                </ul>

                <p><strong>🔗 Bivariate Tables (GROUP BY):</strong></p>
                <ul>
                    <li><strong>Grouped statistics</strong> - Summary statistics for each group</li>
                    <li><strong>Overall column</strong> - Total statistics across all groups</li>
                    <li><strong>P-values</strong> - Statistical significance tests between groups</li>
                    <li><strong>Professional formatting</strong> - Publication-ready tables</li>
                </ul>

                <p><strong>Advanced TABULATE Features:</strong></p>
                <ul>
                    <li><strong>Multiple variables</strong> - Include multiple numeric and categorical variables</li>
                    <li><strong>WHERE clause</strong> - Filter data before creating summary table</li>
                    <li><strong>LIMIT clause</strong> - Limit number of data points for performance</li>
                    <li><strong>GROUP BY clause</strong> - Create bivariate tables with statistical tests</li>
                    <li><strong>Mixed data types</strong> - Automatically handles numeric and categorical variables</li>
                    <li><strong>HTML output</strong> - Beautiful, interactive tables in the browser</li>
                </ul>

                <div class="alert alert-warning">
                    <strong>Requirements:</strong><br>
                    • <strong>VISUALIZE commands:</strong> R with ggplot2, dplyr, and viridis packages<br>
                    • <strong>TABULATE commands:</strong> R with gtsummary, dplyr, and gt packages<br>
                    <br><small><strong>Install VISUALIZE packages:</strong> <code>install.packages(c("ggplot2", "dplyr", "viridis"))</code></small>
                    <br><small><strong>Install TABULATE packages:</strong> <code>install.packages(c("gtsummary", "dplyr", "gt"))</code></small>
                    <br><small><strong>VISUALIZE Output:</strong> High-quality PNG images (150 DPI) for optimal text rendering</small>
                    <br><small><strong>TABULATE Output:</strong> Professional HTML tables with statistical formatting</small>
                </div>

                <h6>Keyboard Shortcuts:</h6>
                <ul>
                    <li><kbd>Ctrl+Enter</kbd> - Run query</li>
                    <li><kbd>Ctrl+A</kbd> - Select all</li>
                    <li><kbd>Ctrl+/</kbd> - Toggle comment</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/sql/sql.min.js"></script>
<script>
// Define custom SQL mode with VISUALIZE and SUMMARIZE keywords
CodeMirror.defineMode("harmattan-sql", function(config, parserConfig) {
    const sqlMode = CodeMirror.getMode(config, "text/x-sql");

    return {
        startState: function() {
            return {
                sqlState: CodeMirror.startState(sqlMode)
            };
        },

        token: function(stream, state) {
            // Check for custom keywords first
            if (stream.match(/\b(VISUALIZE|SUMMARIZE|DESCRIBE|BOXPLOT|BARCHART|SCATTERPLOT|TABULATE)\b/i)) {
                return "keyword custom-keyword";
            }

            // Fall back to standard SQL highlighting
            return sqlMode.token(stream, state.sqlState);
        },

        indent: function(state, textAfter) {
            return sqlMode.indent(state.sqlState, textAfter);
        },

        electricChars: sqlMode.electricChars,
        blockCommentStart: sqlMode.blockCommentStart,
        blockCommentEnd: sqlMode.blockCommentEnd,
        lineComment: sqlMode.lineComment
    };
});

// Initialize CodeMirror with custom mode
const editor = CodeMirror.fromTextArea(document.getElementById('sqlEditor'), {
    mode: 'harmattan-sql',
    theme: 'default',
    lineNumbers: true,
    lineWrapping: true,
    indentUnit: 2,
    smartIndent: true,
    extraKeys: {
        'Ctrl-Enter': runQuery,
        'Cmd-Enter': runQuery
    }
});

// Run query function
function runQuery() {
    const query = editor.getSelection() || editor.getValue();
    
    if (!query.trim()) {
        alert('Please enter a SQL query');
        return;
    }

    // Show loading
    document.getElementById('resultsContent').innerHTML = `
        <div class="loading">
            <i class="fa fa-spinner fa-spin fa-2x mb-3"></i>
            <p>Executing query...</p>
        </div>
    `;

    // Execute query
    fetch('/sql/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: query })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Check for visualization commands (including bivariate ones)
            const visualizationCommands = ['VISUALIZE', 'SCATTERPLOT', 'GROUPED_BOXPLOT', 'GROUPED_BARCHART'];

            if (visualizationCommands.includes(data.custom_command)) {
                displayVisualizationResults(data);
            } else if (data.custom_command === 'DESCRIBE') {
                displayDescribeResults(data);
            } else if (data.custom_command === 'TABULATE') {
                displayTabulateResults(data);
            } else {
                displayResults(data);
            }
        } else {
            displayError(data.error);
        }
    })
    .catch(error => {
        displayError('Network error: ' + error.message);
    });
}

function displayResults(data) {
    const { columns, data: rows, row_count, execution_time } = data;
    
    let html = `
        <div class="query-info">
            <strong>${row_count}</strong> rows returned in <strong>${execution_time}s</strong>
        </div>
    `;

    if (rows.length > 0) {
        html += `
            <div class="table-container">
                <table class="table table-sm table-striped results-table mb-0">
                    <thead>
                        <tr>
                            ${columns.map(col => `<th>${col}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${rows.map(row => `
                            <tr>
                                ${row.map(cell => `<td>${cell !== null ? cell : '<em>NULL</em>'}</td>`).join('')}
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    } else {
        html += '<div class="text-center py-4 text-muted">Query executed successfully but returned no rows.</div>';
    }

    document.getElementById('resultsContent').innerHTML = html;
}

function displayError(error) {
    document.getElementById('resultsContent').innerHTML = `
        <div class="error-message">
            <strong>Error:</strong> ${error}
        </div>
    `;
}

function displayVisualizationResults(data) {
    // Safely extract properties with defaults
    const visualizations = data.visualizations || [];
    const column = data.column;
    const columns = data.columns;
    const table = data.table || '';
    const data_points = data.data_points || 0;
    const execution_time = data.execution_time || 0;
    const where_clause = data.where_clause;
    const limit_clause = data.limit_clause;
    const query_used = data.query_used || '';
    const custom_command = data.custom_command;
    const x_column = data.x_column;
    const y_column = data.y_column;
    const numeric_column = data.numeric_column;
    const group_column = data.group_column;
    const main_column = data.main_column;

    // Handle different command types
    let queryDescription = '';

    if (custom_command === 'SCATTERPLOT') {
        queryDescription = `<strong>${table}.${x_column} vs ${table}.${y_column}</strong>`;
    } else if (custom_command === 'GROUPED_BOXPLOT') {
        queryDescription = `<strong>${table}.${numeric_column} by ${table}.${group_column}</strong>`;
    } else if (custom_command === 'GROUPED_BARCHART') {
        queryDescription = `<strong>${table}.${main_column} by ${table}.${group_column}</strong>`;
    } else {
        // Handle single and multiple column cases
        const columnsList = columns || [column];
        const columnsDisplay = columnsList.join(', ');
        queryDescription = `<strong>${table}.${columnsDisplay}</strong>`;
    }
    let filterInfo = [];

    if (where_clause) {
        filterInfo.push(`WHERE: ${where_clause}`);
    }
    if (limit_clause) {
        filterInfo.push(`LIMIT: ${limit_clause}`);
    }

    if (filterInfo.length > 0) {
        queryDescription += ` (${filterInfo.join(', ')})`;
    }

    // Get command display name
    const commandDisplay = custom_command || 'VISUALIZE';

    let html = `
        <div class="query-info">
            <strong>${commandDisplay}</strong> command executed: <strong>${data_points}</strong> data points from ${queryDescription} in <strong>${execution_time}s</strong>
        </div>
    `;

    // Add query details if filters were used
    if (where_clause || limit_clause) {
        html += `
            <div class="alert alert-info mx-3 mt-2 mb-0">
                <small><strong>Query executed:</strong> <code>${query_used}</code></small>
            </div>
        `;
    }

    if (Array.isArray(visualizations) && visualizations.length > 0) {
        // Check visualization type
        const isMultiColumn = visualizations.some(viz => viz && viz.column !== undefined);
        const isBivariateCommand = ['SCATTERPLOT', 'GROUPED_BOXPLOT', 'GROUPED_BARCHART'].includes(custom_command);

        if (isBivariateCommand) {
            // Handle bivariate visualizations (scatterplot, grouped boxplot, grouped barchart)
            if (custom_command === 'GROUPED_BARCHART') {
                // Special 2-column layout for grouped barcharts
                html += '<div class="visualization-container bivariate-barchart">';
            } else {
                // Single visualization for scatterplot and grouped boxplot
                html += '<div class="visualization-container bivariate-single">';
            }

            visualizations.forEach(viz => {
                const imageContent = viz.svg || viz.png;
                const isBase64 = viz.png !== undefined;

                html += `
                    <div class="visualization-item">
                        <h6>${viz.title}</h6>
                        <div class="image-container" onclick="openFullscreen(this)">
                            ${isBase64 ?
                                `<img src="data:image/png;base64,${imageContent}" alt="${viz.title}" style="width: 100%; height: auto;">` :
                                imageContent
                            }
                        </div>
                    </div>
                `;
            });

            html += '</div>';
        } else if (isMultiColumn) {
            // Organize by plot type for multi-column (both numeric and categorical)
            const numericPlotTypes = ['histogram', 'boxplot', 'density', 'violin'];
            const numericPlotTitles = ['Histograms', 'Box Plots', 'Density Plots', 'Violin Plots'];
            const categoricalPlotTypes = ['bar_chart', 'pie_chart', 'doughnut_chart'];
            const categoricalPlotTitles = ['Bar Charts', 'Pie Charts', 'Doughnut Charts'];

            // Combine all plot types
            const allPlotTypes = [...numericPlotTypes, ...categoricalPlotTypes];
            const allPlotTitles = [...numericPlotTitles, ...categoricalPlotTitles];

            allPlotTypes.forEach((plotType, index) => {
                const plotsOfType = visualizations.filter(viz => viz.plot_type === plotType);

                if (plotsOfType.length > 0) {
                    html += `
                        <div class="plot-type-section">
                            <h5 class="plot-type-title">${allPlotTitles[index]}</h5>
                            <div class="visualization-container">
                    `;

                    plotsOfType.forEach(viz => {
                        const imageContent = viz.svg || viz.png;
                        const isBase64 = viz.png !== undefined;

                        html += `
                            <div class="visualization-item">
                                <h6>${viz.title}</h6>
                                <div class="image-container" onclick="openFullscreen(this)">
                                    ${isBase64 ?
                                        `<img src="data:image/png;base64,${imageContent}" alt="${viz.title}" style="width: 100%; height: auto;">` :
                                        imageContent
                                    }
                                </div>
                            </div>
                        `;
                    });

                    html += `
                            </div>
                        </div>
                    `;
                }
            });
        } else {
            // Single column visualization - use 3-column layout as requested
            html += '<div class="visualization-container single-variable">';

            visualizations.forEach(viz => {
                const imageContent = viz.svg || viz.png;
                const isBase64 = viz.png !== undefined;

                html += `
                    <div class="visualization-item">
                        <h6>${viz.title}</h6>
                        <div class="image-container" onclick="openFullscreen(this)">
                            ${isBase64 ?
                                `<img src="data:image/png;base64,${imageContent}" alt="${viz.title}" style="width: 100%; height: auto;">` :
                                imageContent
                            }
                        </div>
                    </div>
                `;
            });

            html += '</div>';
        }

        // Add fullscreen modal
        html += `
            <div class="modal fade" id="fullscreenModal" tabindex="-1">
                <div class="modal-dialog modal-fullscreen">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Visualization - Full Screen</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body d-flex justify-content-center align-items-center">
                            <div id="fullscreenContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    } else {
        html += '<div class="text-center py-4 text-muted">No visualizations generated.</div>';
    }

    document.getElementById('resultsContent').innerHTML = html;
}

function openFullscreen(element) {
    const svgContent = element.innerHTML;
    const modal = new bootstrap.Modal(document.getElementById('fullscreenModal'));
    document.getElementById('fullscreenContent').innerHTML = svgContent;

    // Scale SVG to fit fullscreen
    const svg = document.querySelector('#fullscreenContent svg');
    if (svg) {
        svg.style.width = '90vw';
        svg.style.height = '90vh';
        svg.style.maxWidth = '100%';
        svg.style.maxHeight = '100%';
    }

    modal.show();
}

function displayDescribeResults(data) {
    const { table, columns, row_count, execution_time } = data;

    let html = `
        <div class="query-info">
            <strong>DESCRIBE</strong> command executed: Table <strong>${table}</strong> with <strong>${columns.length}</strong> columns and <strong>${row_count}</strong> rows in <strong>${execution_time}s</strong>
        </div>
    `;

    if (columns && columns.length > 0) {
        html += `
            <div class="table-container">
                <table class="table table-sm table-striped results-table mb-0">
                    <thead>
                        <tr>
                            <th>Column Name</th>
                            <th>Data Type</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        columns.forEach(col => {
            const isNumeric = col.type.toLowerCase().includes('int') ||
                             col.type.toLowerCase().includes('float') ||
                             col.type.toLowerCase().includes('double') ||
                             col.type.toLowerCase().includes('decimal') ||
                             col.type.toLowerCase().includes('numeric');

            const isCategorical = !isNumeric; // All non-numeric columns are considered categorical

            html += `
                <tr>
                    <td><strong>${col.name}</strong></td>
                    <td>
                        <span class="badge bg-secondary">${col.type}</span>
                        ${isNumeric ? '<span class="badge bg-info ms-1">Numeric</span>' : '<span class="badge bg-warning ms-1">Categorical</span>'}
                    </td>
                    <td>
            `;

            if (isNumeric) {
                html += `
                    <button class="btn btn-sm btn-outline-primary" onclick="visualizeColumn('${col.name}', '${table}')">
                        <i class="fa fa-chart-bar"></i> Numeric Plots
                    </button>
                `;
            } else if (isCategorical) {
                html += `
                    <button class="btn btn-sm btn-outline-success" onclick="visualizeColumn('${col.name}', '${table}')">
                        <i class="fa fa-pie-chart"></i> Categorical Plots
                    </button>
                `;
            }

            html += `
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;
    } else {
        html += '<div class="text-center py-4 text-muted">No column information available.</div>';
    }

    document.getElementById('resultsContent').innerHTML = html;
}

function displayTabulateResults(data) {
    const { table_html, columns, table, data_points, execution_time, where_clause, group_by_clause, limit_clause, query_used } = data;

    // Build query description
    const columnsDisplay = columns.join(', ');
    let queryDescription = `<strong>${table}.${columnsDisplay}</strong>`;

    if (group_by_clause) {
        queryDescription += ` grouped by <strong>${group_by_clause}</strong>`;
    }

    let filterInfo = [];
    if (where_clause) {
        filterInfo.push(`WHERE: ${where_clause}`);
    }
    if (limit_clause) {
        filterInfo.push(`LIMIT: ${limit_clause}`);
    }

    if (filterInfo.length > 0) {
        queryDescription += ` (${filterInfo.join(', ')})`;
    }

    let html = `
        <div class="query-info">
            <strong>TABULATE</strong> command executed: <strong>${data_points}</strong> data points from ${queryDescription} in <strong>${execution_time}s</strong>
        </div>
    `;

    // Add query details if filters were used
    if (where_clause || limit_clause || group_by_clause) {
        html += `
            <div class="alert alert-info mx-3 mt-2 mb-0">
                <small><strong>Query executed:</strong> <code>${query_used}</code></small>
            </div>
        `;
    }

    // Add the summary table using same structure as SELECT results
    if (table_html) {
        html += `
            <div class="table-container">
                <div class="table-summary-container">
                    ${table_html}
                </div>
            </div>
        `;
    } else {
        html += '<div class="text-center py-4 text-muted">No summary table generated.</div>';
    }

    document.getElementById('resultsContent').innerHTML = html;
}

function visualizeColumn(columnName, tableName) {
    const query = `VISUALIZE ${columnName} FROM ${tableName};`;
    editor.setValue(query);
    runQuery();
}

// Event listeners
document.getElementById('runQueryBtn').addEventListener('click', runQuery);
document.getElementById('clearBtn').addEventListener('click', () => {
    editor.setValue('');
    document.getElementById('resultsContent').innerHTML = `
        <div class="text-center py-5 text-muted">
            <i class="fa fa-code fa-2x mb-3"></i>
            <p>Run a SQL query to see results here</p>
        </div>
    `;
});

// Set focus to editor
editor.focus();
</script>
{% endblock %}
