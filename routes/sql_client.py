from flask import Blueprint, render_template, request, jsonify
from routes.auth import login_required
try:
    from models.database import DatabaseManager
except ImportError:
    from models.database_simple import DatabaseManager
from services.custom_sql_service import CustomSQLService
import traceback

sql_client_bp = Blueprint('sql_client', __name__, url_prefix='/sql')

@sql_client_bp.route('/')
@login_required
def index():
    """SQL Client interface"""
    db_manager = DatabaseManager()
    tables = db_manager.list_tables()
    return render_template('sql_client/index.html', tables=tables)

@sql_client_bp.route('/execute', methods=['POST'])
@login_required
def execute_sql():
    """Execute SQL query and return results"""
    try:
        data = request.get_json()
        sql_query = data.get('query', '').strip()

        if not sql_query:
            return jsonify({
                'success': False,
                'error': 'No SQL query provided'
            })

        db_manager = DatabaseManager()
        custom_sql_service = CustomSQLService(db_manager)

        # Check if this is a custom SQL command
        if custom_sql_service.is_custom_sql(sql_query):
            # Check R availability for visualization commands
            if 'VISUALIZE' in sql_query.upper() and not custom_sql_service.check_r_availability():
                return jsonify({
                    'success': False,
                    'error': 'R and required packages (ggplot2, dplyr) are not available. Please install R and the required packages to use VISUALIZE commands.'
                })

            # Check gtsummary availability for tabulation commands
            if 'TABULATE' in sql_query.upper() and not custom_sql_service.check_gtsummary_availability():
                return jsonify({
                    'success': False,
                    'error': 'R and required packages (gtsummary, dplyr, gt) are not available. Please install R and run: install.packages(c("gtsummary", "dplyr", "gt")) to use TABULATE commands.'
                })

            # Execute custom SQL command
            result = custom_sql_service.execute_custom_sql(sql_query)
            return jsonify(result)

        # Execute regular SQL query
        result = db_manager.execute_query(sql_query)

        if result['success']:
            return jsonify({
                'success': True,
                'data': result['data'],
                'columns': result['columns'],
                'row_count': result['row_count'],
                'execution_time': result['execution_time']
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Server error: {str(e)}',
            'traceback': traceback.format_exc()
        })

@sql_client_bp.route('/tables')
@login_required
def get_tables():
    """Get list of available tables"""
    try:
        db_manager = DatabaseManager()
        tables = db_manager.list_tables()
        return jsonify({
            'success': True,
            'tables': tables
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@sql_client_bp.route('/table_schema/<table_name>')
@login_required
def get_table_schema(table_name):
    """Get schema information for a specific table"""
    try:
        db_manager = DatabaseManager()
        schema = db_manager.get_table_schema(table_name)
        return jsonify({
            'success': True,
            'schema': schema
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })
