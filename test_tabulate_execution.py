#!/usr/bin/env python3
"""
Test script for TABULATE execution functionality
"""

import sys
import os
import tempfile
import csv
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.custom_sql_service import CustomSQLService

class MockDatabaseManager:
    """Mock database manager for testing"""
    def execute_query(self, query):
        # Return sample data that mimics a real database result
        return {
            'success': True,
            'data': [
                ('<PERSON>', 25, 'Male', 70.5, 'Bachelor'),
                ('<PERSON>', 30, 'Female', 65.2, 'Master'),
                ('<PERSON>', 35, 'Male', 80.1, 'Bachelor'),
                ('<PERSON>', 28, 'Female', 62.8, 'PhD'),
                ('<PERSON>', 32, 'Male', 75.3, 'Master'),
                ('<PERSON>', 27, 'Female', 58.9, 'Bachelor')
            ],
            'columns': ['name', 'age', 'gender', 'weight', 'education'],
            'row_count': 6
        }

def test_tabulate_execution():
    """Test TABULATE command execution"""
    print("Testing TABULATE command execution...")
    
    # Create service with mock database
    mock_db = MockDatabaseManager()
    service = CustomSQLService(mock_db)
    
    # Test cases
    test_cases = [
        {
            'name': 'Basic univariate table',
            'query': 'TABULATE age, weight FROM users;'
        },
        {
            'name': 'Mixed numeric and categorical',
            'query': 'TABULATE age, gender, education FROM users;'
        },
        {
            'name': 'With WHERE clause',
            'query': 'TABULATE age, weight FROM users WHERE age > 25;'
        },
        {
            'name': 'With LIMIT clause',
            'query': 'TABULATE age, gender FROM users LIMIT 3;'
        },
        {
            'name': 'Bivariate with GROUP BY',
            'query': 'TABULATE age, weight FROM users GROUP BY gender;'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print(f"Query: {test_case['query']}")
        
        try:
            # Execute the TABULATE command
            result = service.execute_custom_sql(test_case['query'])
            
            if result['success']:
                print(f"  ✓ Execution successful!")
                print(f"  ✓ Data points: {result.get('data_points', 'N/A')}")
                print(f"  ✓ Execution time: {result.get('execution_time', 'N/A')}s")
                print(f"  ✓ Query used: {result.get('query_used', 'N/A')}")
                
                # Check if HTML table was generated
                if 'table_html' in result and result['table_html']:
                    print(f"  ✓ HTML table generated ({len(result['table_html'])} characters)")
                    
                    # Save a sample of the HTML for inspection
                    if i == 1:  # Save first test result
                        with open('sample_tabulate_output.html', 'w') as f:
                            f.write(result['table_html'])
                        print(f"  ✓ Sample HTML saved to sample_tabulate_output.html")
                else:
                    print(f"  ✗ No HTML table generated")
                    
            else:
                print(f"  ✗ Execution failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"  ✗ Exception occurred: {str(e)}")
    
    print("\n" + "="*60)
    print("TABULATE execution tests completed!")

if __name__ == "__main__":
    test_tabulate_execution()
