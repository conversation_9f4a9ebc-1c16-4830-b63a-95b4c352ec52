#!/usr/bin/env python3
"""
Test script for TABULATE functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.custom_sql_service import CustomSQLService

class MockDatabaseManager:
    """Mock database manager for testing"""
    def execute_query(self, query):
        return {
            'success': True,
            'data': [
                ('<PERSON>', 25, 'Male'),
                ('<PERSON>', 30, 'Female'),
                ('<PERSON>', 35, 'Male'),
                ('Alice', 28, 'Female')
            ],
            'columns': ['name', 'age', 'gender'],
            'row_count': 4
        }

def test_tabulate_parsing():
    """Test TABULATE command parsing"""
    print("Testing TABULATE command parsing...")
    
    # Create service with mock database
    mock_db = MockDatabaseManager()
    service = CustomSQLService(mock_db)
    
    # Test cases
    test_cases = [
        # Basic TABULATE
        "TABULATE age, gender FROM users;",
        # With WHERE clause
        "TABULATE age, gender FROM users WHERE age > 25;",
        # With LIMIT clause
        "TABULATE age, gender FROM users LIMIT 100;",
        # With GROUP BY clause
        "TABULATE age FROM users GROUP BY gender;",
        # Combined clauses
        "TABULATE age, income FROM users WHERE age > 18 GROUP BY gender LIMIT 500;",
        # Multiple variables
        "TABULATE age, height, weight, gender, education FROM users;",
    ]
    
    for i, query in enumerate(test_cases, 1):
        print(f"\nTest {i}: {query}")
        
        # Test if it's recognized as custom SQL
        is_custom = service.is_custom_sql(query)
        print(f"  Recognized as custom SQL: {is_custom}")
        
        # Test parsing
        parsed = service.parse_tabulate_command(query)
        if parsed:
            print(f"  Parsed successfully:")
            print(f"    Command: {parsed['command']}")
            print(f"    Columns: {parsed['columns']}")
            print(f"    Table: {parsed['table']}")
            if 'where' in parsed:
                print(f"    WHERE: {parsed['where']}")
            if 'group_by' in parsed:
                print(f"    GROUP BY: {parsed['group_by']}")
            if 'limit' in parsed:
                print(f"    LIMIT: {parsed['limit']}")
        else:
            print(f"  Failed to parse!")
    
    print("\n" + "="*50)
    print("TABULATE parsing tests completed!")

def test_gtsummary_availability():
    """Test gtsummary package availability"""
    print("Testing gtsummary availability...")
    
    mock_db = MockDatabaseManager()
    service = CustomSQLService(mock_db)
    
    available = service.check_gtsummary_availability()
    print(f"gtsummary available: {available}")
    
    if not available:
        print("Note: gtsummary not available. Install with:")
        print("  install.packages(c('gtsummary', 'dplyr', 'gt'))")

if __name__ == "__main__":
    test_tabulate_parsing()
    print()
    test_gtsummary_availability()
