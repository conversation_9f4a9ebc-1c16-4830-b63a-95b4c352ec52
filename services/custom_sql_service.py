import re
import subprocess
import tempfile
import os
from pathlib import Path
import json
import time
import logging
from typing import Dict, Any, List, Tuple, Optional

logger = logging.getLogger(__name__)

class CustomSQLService:
    """Service to handle custom SQL commands like VISUALIZE and SUMMARIZ<PERSON>"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.r_script_dir = Path('r_scripts')
        self.r_script_dir.mkdir(exist_ok=True)
        
    def is_custom_sql(self, query: str) -> bool:
        """Check if query contains custom SQL commands"""
        query_upper = query.upper().strip()
        custom_keywords = ['VISUALIZE', 'SUMMARIZE', 'DESCRIBE', 'TABULATE']
        return any(keyword in query_upper for keyword in custom_keywords)
    
    def parse_visualize_command(self, query: str) -> Optional[Dict[str, str]]:
        """Parse VISUALIZE command and extract parameters with WHERE and LIMIT support"""
        # Enhanced pattern to support multiple columns, WHERE and LIMIT clauses
        # VISUALIZE column1, column2, column3 FROM table [WHERE conditions] [LIMIT number];
        # VISUALIZE SCATTERPLOT(col1, col2) FROM table;
        # VISUALIZE BOXPLOT(numeric_col) FROM table GROUP BY categorical_col;
        # VISUALIZE BARCHART(cat_col1) FROM table GROUP BY cat_col2;

        # Remove trailing semicolon and normalize whitespace
        query_clean = re.sub(r'\s*;\s*$', '', query.strip())

        # Check for SCATTERPLOT syntax
        scatterplot_pattern = r'VISUALIZE\s+SCATTERPLOT\s*\(\s*(\w+)\s*,\s*(\w+)\s*\)\s+FROM\s+(\w+)'
        scatterplot_match = re.search(scatterplot_pattern, query_clean, re.IGNORECASE)
        if scatterplot_match:
            return self._parse_scatterplot_command(query_clean, scatterplot_match)

        # Check for BOXPLOT with GROUP BY syntax
        boxplot_pattern = r'VISUALIZE\s+BOXPLOT\s*\(\s*(\w+)\s*\)\s+FROM\s+(\w+)\s+GROUP\s+BY\s+(\w+)'
        boxplot_match = re.search(boxplot_pattern, query_clean, re.IGNORECASE)
        if boxplot_match:
            return self._parse_grouped_boxplot_command(query_clean, boxplot_match)

        # Check for BARCHART with GROUP BY syntax
        barchart_pattern = r'VISUALIZE\s+BARCHART\s*\(\s*(\w+)\s*\)\s+FROM\s+(\w+)\s+GROUP\s+BY\s+(\w+)'
        barchart_match = re.search(barchart_pattern, query_clean, re.IGNORECASE)
        if barchart_match:
            return self._parse_grouped_barchart_command(query_clean, barchart_match)

        # Standard VISUALIZE pattern: VISUALIZE column1, column2, ... FROM table
        base_pattern = r'VISUALIZE\s+([\w\s,]+)\s+FROM\s+(\w+)'
        base_match = re.search(base_pattern, query_clean, re.IGNORECASE)

        if not base_match:
            return None

        # Parse multiple columns (comma-separated)
        columns_str = base_match.group(1).strip()
        columns = [col.strip() for col in columns_str.split(',') if col.strip()]
        table = base_match.group(2)

        # Extract WHERE clause if present
        where_clause = None
        where_pattern = r'WHERE\s+(.+?)(?:\s+LIMIT\s+\d+\s*$|$)'
        where_match = re.search(where_pattern, query_clean, re.IGNORECASE)
        if where_match:
            where_clause = where_match.group(1).strip()

        # Extract LIMIT clause if present
        limit_clause = None
        limit_pattern = r'LIMIT\s+(\d+)\s*$'
        limit_match = re.search(limit_pattern, query_clean, re.IGNORECASE)
        if limit_match:
            limit_clause = int(limit_match.group(1))

        result = {
            'command': 'VISUALIZE',
            'columns': columns,  # Changed from 'column' to 'columns' (list)
            'table': table
        }

        if where_clause:
            result['where'] = where_clause

        if limit_clause:
            result['limit'] = limit_clause

        return result

    def _parse_scatterplot_command(self, query_clean: str, match) -> Dict[str, str]:
        """Parse SCATTERPLOT command"""
        x_column = match.group(1)
        y_column = match.group(2)
        table = match.group(3)

        # Extract WHERE and LIMIT clauses
        where_clause = None
        where_pattern = r'WHERE\s+(.+?)(?:\s+LIMIT\s+\d+\s*$|$)'
        where_match = re.search(where_pattern, query_clean, re.IGNORECASE)
        if where_match:
            where_clause = where_match.group(1).strip()

        limit_clause = None
        limit_pattern = r'LIMIT\s+(\d+)\s*$'
        limit_match = re.search(limit_pattern, query_clean, re.IGNORECASE)
        if limit_match:
            limit_clause = int(limit_match.group(1))

        result = {
            'command': 'SCATTERPLOT',
            'x_column': x_column,
            'y_column': y_column,
            'table': table
        }

        if where_clause:
            result['where'] = where_clause
        if limit_clause:
            result['limit'] = limit_clause

        return result

    def _parse_grouped_boxplot_command(self, query_clean: str, match) -> Dict[str, str]:
        """Parse grouped BOXPLOT command"""
        numeric_column = match.group(1)
        table = match.group(2)
        group_column = match.group(3)

        # Extract WHERE and LIMIT clauses
        where_clause = None
        where_pattern = r'WHERE\s+(.+?)(?:\s+GROUP\s+BY|\s+LIMIT\s+\d+\s*$|$)'
        where_match = re.search(where_pattern, query_clean, re.IGNORECASE)
        if where_match:
            where_clause = where_match.group(1).strip()

        limit_clause = None
        limit_pattern = r'LIMIT\s+(\d+)\s*$'
        limit_match = re.search(limit_pattern, query_clean, re.IGNORECASE)
        if limit_match:
            limit_clause = int(limit_match.group(1))

        result = {
            'command': 'GROUPED_BOXPLOT',
            'numeric_column': numeric_column,
            'group_column': group_column,
            'table': table
        }

        if where_clause:
            result['where'] = where_clause
        if limit_clause:
            result['limit'] = limit_clause

        return result

    def _parse_grouped_barchart_command(self, query_clean: str, match) -> Dict[str, str]:
        """Parse grouped BARCHART command"""
        main_column = match.group(1)
        table = match.group(2)
        group_column = match.group(3)

        # Extract WHERE and LIMIT clauses
        where_clause = None
        where_pattern = r'WHERE\s+(.+?)(?:\s+GROUP\s+BY|\s+LIMIT\s+\d+\s*$|$)'
        where_match = re.search(where_pattern, query_clean, re.IGNORECASE)
        if where_match:
            where_clause = where_match.group(1).strip()

        limit_clause = None
        limit_pattern = r'LIMIT\s+(\d+)\s*$'
        limit_match = re.search(limit_pattern, query_clean, re.IGNORECASE)
        if limit_match:
            limit_clause = int(limit_match.group(1))

        result = {
            'command': 'GROUPED_BARCHART',
            'main_column': main_column,
            'group_column': group_column,
            'table': table
        }

        if where_clause:
            result['where'] = where_clause
        if limit_clause:
            result['limit'] = limit_clause

        return result

    def parse_describe_command(self, query: str) -> Optional[Dict[str, str]]:
        """Parse DESCRIBE command and extract parameters"""
        # Pattern: DESCRIBE table_name;
        pattern = r'DESCRIBE\s+(\w+)\s*;?'
        match = re.search(pattern, query, re.IGNORECASE)

        if match:
            return {
                'command': 'DESCRIBE',
                'table': match.group(1)
            }
        return None

    def parse_tabulate_command(self, query: str) -> Optional[Dict[str, str]]:
        """Parse TABULATE command and extract parameters with WHERE, LIMIT, and GROUP BY support"""
        # TABULATE var1, var2, var3 FROM table [WHERE conditions] [GROUP BY var] [LIMIT number];

        # Remove trailing semicolon and normalize whitespace
        query_clean = re.sub(r'\s*;\s*$', '', query.strip())

        # Base pattern: TABULATE column1, column2, ... FROM table
        base_pattern = r'TABULATE\s+([\w\s,]+)\s+FROM\s+(\w+)'
        base_match = re.search(base_pattern, query_clean, re.IGNORECASE)

        if not base_match:
            return None

        # Parse multiple columns (comma-separated)
        columns_str = base_match.group(1).strip()
        columns = [col.strip() for col in columns_str.split(',') if col.strip()]
        table = base_match.group(2)

        # Extract WHERE clause if present
        where_clause = None
        where_pattern = r'WHERE\s+(.+?)(?:\s+GROUP\s+BY\s+\w+|\s+LIMIT\s+\d+\s*$|$)'
        where_match = re.search(where_pattern, query_clean, re.IGNORECASE)
        if where_match:
            where_clause = where_match.group(1).strip()

        # Extract GROUP BY clause if present
        group_by_clause = None
        group_by_pattern = r'GROUP\s+BY\s+(\w+)'
        group_by_match = re.search(group_by_pattern, query_clean, re.IGNORECASE)
        if group_by_match:
            group_by_clause = group_by_match.group(1).strip()

        # Extract LIMIT clause if present
        limit_clause = None
        limit_pattern = r'LIMIT\s+(\d+)\s*$'
        limit_match = re.search(limit_pattern, query_clean, re.IGNORECASE)
        if limit_match:
            limit_clause = int(limit_match.group(1))

        result = {
            'command': 'TABULATE',
            'columns': columns,
            'table': table
        }

        if where_clause:
            result['where'] = where_clause
        if group_by_clause:
            result['group_by'] = group_by_clause
        if limit_clause:
            result['limit'] = limit_clause

        return result
    
    def execute_custom_sql(self, query: str) -> Dict[str, Any]:
        """Execute custom SQL command"""
        start_time = time.time()
        
        try:
            # Parse VISUALIZE command (includes new bivariate commands)
            visualize_params = self.parse_visualize_command(query)
            if visualize_params:
                command_type = visualize_params['command']

                if command_type == 'VISUALIZE':
                    return self._execute_visualize(visualize_params, start_time)
                elif command_type == 'SCATTERPLOT':
                    return self._execute_scatterplot(visualize_params, start_time)
                elif command_type == 'GROUPED_BOXPLOT':
                    return self._execute_grouped_boxplot(visualize_params, start_time)
                elif command_type == 'GROUPED_BARCHART':
                    return self._execute_grouped_barchart(visualize_params, start_time)

            # Parse DESCRIBE command
            describe_params = self.parse_describe_command(query)
            if describe_params:
                return self._execute_describe(describe_params, start_time)

            # Parse TABULATE command
            tabulate_params = self.parse_tabulate_command(query)
            if tabulate_params:
                return self._execute_tabulate(tabulate_params, start_time)

            # Add more custom commands here (SUMMARIZE, etc.)
            
            return {
                'success': False,
                'error': 'Unknown custom SQL command',
                'execution_time': round(time.time() - start_time, 3)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Custom SQL execution error: {str(e)}',
                'execution_time': round(time.time() - start_time, 3)
            }
    
    def _execute_visualize(self, params: Dict[str, str], start_time: float) -> Dict[str, Any]:
        """Execute VISUALIZE command with multiple columns, WHERE and LIMIT support"""
        columns = params['columns']  # Now a list of columns
        table = params['table']
        where_clause = params.get('where')
        limit_clause = params.get('limit')

        try:
            # First, validate that the table exists
            table_info = self.db_manager.get_table_info(table)
            if not table_info:
                raise ValueError(f"Table '{table}' not found")

            # Check if all columns exist (case-insensitive) and get actual names and types
            column_names = [col['name'] for col in table_info['columns']]
            column_types = {col['name']: col['type'] for col in table_info['columns']}
            column_names_lower = [name.lower() for name in column_names]

            actual_columns = []
            column_data_types = []

            for column in columns:
                # Find the actual column name (case-insensitive match)
                actual_column = None
                for i, col_lower in enumerate(column_names_lower):
                    if col_lower == column.lower():
                        actual_column = column_names[i]
                        break

                if actual_column is None:
                    available_columns = ', '.join(column_names)
                    raise ValueError(f"Column '{column}' not found in table '{table}'. Available columns: {available_columns}")

                actual_columns.append(actual_column)
                column_data_types.append(column_types[actual_column])

            # Determine if columns are numeric or categorical
            numeric_columns = []
            categorical_columns = []

            for i, (column, data_type) in enumerate(zip(actual_columns, column_data_types)):
                if self._is_numeric_type(data_type):
                    numeric_columns.append(column)
                else:
                    categorical_columns.append(column)

            # Build the data query with multiple columns
            columns_str = ', '.join(actual_columns)

            # Create WHERE clause for non-null values for all columns
            null_conditions = [f"{col} IS NOT NULL" for col in actual_columns]
            base_where = ' AND '.join(null_conditions)

            data_query = f"SELECT {columns_str} FROM {table} WHERE {base_where}"

            # Add additional WHERE clause if provided
            if where_clause:
                data_query += f" AND ({where_clause})"

            # Add LIMIT clause if provided
            if limit_clause:
                data_query += f" LIMIT {limit_clause}"

            # Execute the query
            data_result = self.db_manager.execute_query(data_query)

            if not data_result['success']:
                raise ValueError(f"Failed to fetch data: {data_result['error']}")

            # Log data retrieval for debugging
            logger.info(f"Retrieved {len(data_result['data'])} rows from SQL query")
            if limit_clause:
                logger.info(f"LIMIT {limit_clause} will be applied in R processing")

            if len(data_result['data']) == 0:
                filter_info = ""
                if where_clause:
                    filter_info += f" with WHERE condition: {where_clause}"
                if limit_clause:
                    filter_info += f" with LIMIT: {limit_clause}"
                columns_display = ', '.join(actual_columns)
                raise ValueError(f"No data found for columns '{columns_display}' in table '{table}'{filter_info}")

            # Generate appropriate visualizations based on column types
            if len(actual_columns) == 1:
                # Single column - determine type and generate appropriate plots
                column = actual_columns[0]
                if column in numeric_columns:
                    visualizations = self._generate_r_visualizations(
                        data_result['data'], column, table, where_clause, limit_clause
                    )
                else:
                    visualizations = self._generate_categorical_r_visualizations(
                        data_result['data'], column, table, where_clause, limit_clause
                    )
            else:
                # Multiple columns - generate mixed visualizations
                visualizations = self._generate_mixed_column_r_visualizations(
                    data_result['data'], actual_columns, numeric_columns, categorical_columns,
                    table, where_clause, limit_clause
                )

            execution_time = round(time.time() - start_time, 3)

            result = {
                'success': True,
                'custom_command': 'VISUALIZE',
                'visualizations': visualizations,
                'columns': actual_columns,  # Return list of columns
                'table': table,
                'data_points': len(data_result['data']),
                'execution_time': execution_time,
                'query_used': data_query
            }

            # Add filter information to result
            if where_clause:
                result['where_clause'] = where_clause
            if limit_clause:
                result['limit_clause'] = limit_clause

            return result

        except Exception as e:
            execution_time = round(time.time() - start_time, 3)
            return {
                'success': False,
                'error': str(e),
                'execution_time': execution_time
            }

    def _execute_describe(self, params: Dict[str, str], start_time: float) -> Dict[str, Any]:
        """Execute DESCRIBE command"""
        table = params['table']

        try:
            # Get table information
            table_info = self.db_manager.get_table_info(table)
            if not table_info:
                raise ValueError(f"Table '{table}' not found")

            execution_time = round(time.time() - start_time, 3)

            return {
                'success': True,
                'custom_command': 'DESCRIBE',
                'table': table,
                'columns': table_info['columns'],
                'row_count': table_info['row_count'],
                'execution_time': execution_time
            }

        except Exception as e:
            execution_time = round(time.time() - start_time, 3)
            return {
                'success': False,
                'error': str(e),
                'execution_time': execution_time
            }

    def _execute_tabulate(self, params: Dict[str, str], start_time: float) -> Dict[str, Any]:
        """Execute TABULATE command using gtsummary package"""
        columns = params['columns']
        table = params['table']
        where_clause = params.get('where')
        group_by_clause = params.get('group_by')
        limit_clause = params.get('limit')

        try:
            # Check if gtsummary is available
            if not self.check_gtsummary_availability():
                raise ValueError("R and gtsummary package are not available. Please install R and run: install.packages('gtsummary')")

            # Build SQL query to fetch data
            data_query = f"SELECT {', '.join(columns)}"
            if group_by_clause:
                data_query += f", {group_by_clause}"
            data_query += f" FROM {table}"

            # Add WHERE clause if provided
            if where_clause:
                data_query += f" WHERE {where_clause}"

            # Add LIMIT clause if provided
            if limit_clause:
                data_query += f" LIMIT {limit_clause}"

            # Execute the query
            data_result = self.db_manager.execute_query(data_query)

            if not data_result['success']:
                raise ValueError(f"Failed to fetch data: {data_result['error']}")

            # Log data retrieval for debugging
            logger.info(f"Retrieved {len(data_result['data'])} rows from SQL query for TABULATE")
            if limit_clause:
                logger.info(f"LIMIT {limit_clause} will be applied in R processing")

            # Generate summary table using gtsummary
            table_html = self._generate_gtsummary_table(
                data_result['data'],
                data_result['columns'],
                columns,
                group_by_clause,
                limit_clause,
                table,
                where_clause
            )

            execution_time = round(time.time() - start_time, 3)

            result = {
                'success': True,
                'custom_command': 'TABULATE',
                'table_html': table_html,
                'columns': columns,
                'table': table,
                'data_points': len(data_result['data']),
                'execution_time': execution_time,
                'query_used': data_query
            }

            # Add filter information to result
            if where_clause:
                result['where_clause'] = where_clause
            if group_by_clause:
                result['group_by_clause'] = group_by_clause
            if limit_clause:
                result['limit_clause'] = limit_clause

            return result

        except Exception as e:
            execution_time = round(time.time() - start_time, 3)
            return {
                'success': False,
                'error': str(e),
                'execution_time': execution_time
            }
    
    def _generate_r_visualizations(self, data: List[Tuple], column: str, table: str,
                                  where_clause: Optional[str] = None,
                                  limit_clause: Optional[int] = None) -> List[Dict[str, str]]:
        """Generate R visualizations using ggplot2 with filter information"""

        # Extract numeric values from data tuples
        values = [float(row[0]) for row in data if row[0] is not None]

        if not values:
            raise ValueError("No numeric values found for visualization")

        # Create R script for visualizations
        r_script = self._create_r_visualization_script(values, column, table, where_clause, limit_clause)

        # Execute R script and get SVG outputs
        return self._execute_r_script(r_script)

    def _generate_multi_column_r_visualizations(self, data: List[Tuple], columns: List[str], table: str,
                                               where_clause: Optional[str] = None,
                                               limit_clause: Optional[int] = None) -> List[Dict[str, str]]:
        """Generate R visualizations for multiple columns using ggplot2"""

        # Organize data by column
        column_data = {}
        for i, column in enumerate(columns):
            values = [float(row[i]) for row in data if row[i] is not None and str(row[i]).replace('.', '').replace('-', '').isdigit()]
            if values:
                column_data[column] = values

        if not column_data:
            raise ValueError("No numeric values found for visualization in any column")

        # Create R script for multi-column visualizations
        r_script = self._create_multi_column_r_script(column_data, table, where_clause, limit_clause)

        # Execute R script and get SVG outputs
        return self._execute_r_script_multi_column(r_script, columns)
    
    def _create_r_visualization_script(self, values: List[float], column: str, table: str,
                                      where_clause: Optional[str] = None,
                                      limit_clause: Optional[int] = None) -> str:
        """Create R script for generating visualizations with filter information"""

        # Convert Python list to R vector
        r_values = ', '.join(map(str, values))

        # Create subtitle with filter information
        subtitle_parts = [f"from {table}"]
        if where_clause:
            # Truncate long WHERE clauses for display
            where_display = where_clause if len(where_clause) <= 40 else where_clause[:37] + "..."
            subtitle_parts.append(f"WHERE {where_display}")
        if limit_clause:
            subtitle_parts.append(f"LIMIT {limit_clause}")

        subtitle_text = " | ".join(subtitle_parts)
        # Calculate actual data points after potential LIMIT
        actual_data_points = min(len(values), limit_clause) if limit_clause else len(values)
        data_points_text = f"({actual_data_points} data points)"

        r_script = f'''
# Load required libraries
library(ggplot2)
library(dplyr)
library(viridis)

# Create data frame
data <- data.frame({column} = c({r_values}))

# Apply LIMIT constraint in R if specified
{f"data <- data %>% head({limit_clause})" if limit_clause else "# No LIMIT constraint"}

# Set up PNG output directory
output_dir <- "r_scripts/output"
dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)

# Function to save plot as PNG
save_png <- function(plot, filename, width = 800, height = 600, dpi = 150) {{
    png(file.path(output_dir, filename), width = width, height = height, res = dpi)
    print(plot)
    dev.off()
}}

# Create subtitle with filter information
subtitle_text <- "{subtitle_text}"
data_points_text <- "{data_points_text}"
full_subtitle <- paste("{column}", subtitle_text, data_points_text)

# 1. Histogram (ggplot2)
p1 <- ggplot(data, aes(x = {column})) +
    geom_histogram(bins = 30, fill = "#3498db", alpha = 0.8, color = "white", size = 0.2) +
    labs(title = "Histogram",
         subtitle = full_subtitle,
         x = "{column}", y = "Frequency") +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 10),
          panel.grid.minor = element_blank(),
          panel.grid.major.x = element_blank())

save_png(p1, "histogram.png")

# 2. Density Plot (ggplot2)
p2 <- ggplot(data, aes(x = {column})) +
    geom_density(fill = "#e74c3c", alpha = 0.7, color = "#c0392b", size = 1) +
    labs(title = "Density Plot",
         subtitle = full_subtitle,
         x = "{column}", y = "Density") +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 10),
          panel.grid.minor = element_blank(),
          panel.grid.major.x = element_blank())

save_png(p2, "density.png")

# 3. Box Plot (ggplot2)
p3 <- ggplot(data, aes(x = "", y = {column})) +
    geom_boxplot(fill = "#2ecc71", alpha = 0.8, color = "#27ae60", width = 0.6,
                 outlier.color = "#e74c3c", outlier.size = 2) +
    labs(title = "Box Plot",
         subtitle = full_subtitle,
         x = "", y = "{column}") +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 10),
          axis.text.x = element_blank(),
          axis.ticks.x = element_blank(),
          panel.grid.major.x = element_blank())

save_png(p3, "boxplot.png")

# 4. Violin Plot (ggplot2)
p4 <- ggplot(data, aes(x = "", y = {column})) +
    geom_violin(fill = "#9b59b6", alpha = 0.7, color = "#8e44ad", size = 1) +
    geom_boxplot(width = 0.1, fill = "white", alpha = 0.9, color = "#2c3e50") +
    labs(title = "Violin Plot",
         subtitle = full_subtitle,
         x = "", y = "{column}") +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 10),
          axis.text.x = element_blank(),
          axis.ticks.x = element_blank(),
          panel.grid.major.x = element_blank())

save_png(p4, "violin.png")

# Output completion message
cat("Visualizations generated successfully\\n")
'''

        return r_script

    def _create_multi_column_r_script(self, column_data: Dict[str, List[float]], table: str,
                                     where_clause: Optional[str] = None,
                                     limit_clause: Optional[int] = None) -> str:
        """Create R script for generating multi-column visualizations"""

        # Create subtitle with filter information
        subtitle_parts = [f"from {table}"]
        if where_clause:
            where_display = where_clause if len(where_clause) <= 40 else where_clause[:37] + "..."
            subtitle_parts.append(f"WHERE {where_display}")
        if limit_clause:
            subtitle_parts.append(f"LIMIT {limit_clause}")

        subtitle_text = " | ".join(subtitle_parts)

        # Start R script
        r_script = '''
# Load required libraries
library(ggplot2)
library(dplyr)
library(viridis)

# Set up PNG output directory
output_dir <- "r_scripts/output"
dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)

# Function to save plot as PNG
save_png <- function(plot, filename, width = 800, height = 600, dpi = 150) {
    png(file.path(output_dir, filename), width = width, height = height, res = dpi)
    print(plot)
    dev.off()
}

'''

        # Add data for each column
        for column, values in column_data.items():
            r_values = ', '.join(map(str, values))
            # Calculate actual data points after potential LIMIT
            actual_data_points = min(len(values), limit_clause) if limit_clause else len(values)
            data_points_text = f"({actual_data_points} data points)"

            r_script += f'''
# Data for {column}
data_{column} <- data.frame({column} = c({r_values}))

# Apply LIMIT constraint in R if specified
{f"data_{column} <- data_{column} %>% head({limit_clause})" if limit_clause else f"# No LIMIT constraint for {column}"}

subtitle_{column} <- "{column} {subtitle_text} {data_points_text}"

'''

        # Generate plots organized by type
        plot_counter = 1

        # 1. Histograms for all columns (ggplot2)
        r_script += "\n# === HISTOGRAMS ===\n"
        for column in column_data.keys():
            r_script += f'''
p{plot_counter} <- ggplot(data_{column}, aes(x = {column})) +
    geom_histogram(bins = 30, fill = "#3498db", alpha = 0.8, color = "white", size = 0.2) +
    labs(title = "Histogram",
         subtitle = subtitle_{column},
         x = "{column}", y = "Frequency") +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 10),
          panel.grid.minor = element_blank(),
          panel.grid.major.x = element_blank())

save_png(p{plot_counter}, "histogram_{column}.png")

'''
            plot_counter += 1

        # 2. Box plots for all columns (ggplot2)
        r_script += "\n# === BOX PLOTS ===\n"
        for column in column_data.keys():
            r_script += f'''
p{plot_counter} <- ggplot(data_{column}, aes(x = "", y = {column})) +
    geom_boxplot(fill = "#2ecc71", alpha = 0.8, color = "#27ae60", width = 0.6,
                 outlier.color = "#e74c3c", outlier.size = 2) +
    labs(title = "Box Plot",
         subtitle = subtitle_{column},
         x = "", y = "{column}") +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 10),
          axis.text.x = element_blank(),
          axis.ticks.x = element_blank(),
          panel.grid.major.x = element_blank())

save_png(p{plot_counter}, "boxplot_{column}.png")

'''
            plot_counter += 1

        # 3. Density plots for all columns (ggplot2)
        r_script += "\n# === DENSITY PLOTS ===\n"
        for column in column_data.keys():
            r_script += f'''
p{plot_counter} <- ggplot(data_{column}, aes(x = {column})) +
    geom_density(fill = "#e74c3c", alpha = 0.7, color = "#c0392b", size = 1) +
    labs(title = "Density Plot",
         subtitle = subtitle_{column},
         x = "{column}", y = "Density") +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 10),
          panel.grid.minor = element_blank(),
          panel.grid.major.x = element_blank())

save_png(p{plot_counter}, "density_{column}.png")

'''
            plot_counter += 1

        # 4. Violin plots for all columns (ggplot2)
        r_script += "\n# === VIOLIN PLOTS ===\n"
        for column in column_data.keys():
            r_script += f'''
p{plot_counter} <- ggplot(data_{column}, aes(x = "", y = {column})) +
    geom_violin(fill = "#9b59b6", alpha = 0.7, color = "#8e44ad", size = 1) +
    geom_boxplot(width = 0.1, fill = "white", alpha = 0.9, color = "#2c3e50") +
    labs(title = "Violin Plot",
         subtitle = subtitle_{column},
         x = "", y = "{column}") +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 10),
          axis.text.x = element_blank(),
          axis.ticks.x = element_blank(),
          panel.grid.major.x = element_blank())

save_png(p{plot_counter}, "violin_{column}.png")

'''
            plot_counter += 1

        r_script += '''
# Output completion message
cat("Multi-column visualizations generated successfully\\n")
'''

        return r_script
    
    def _execute_r_script(self, r_script: str) -> List[Dict[str, str]]:
        """Execute R script and return SVG content"""
        
        # Create temporary R script file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.R', delete=False) as f:
            f.write(r_script)
            script_path = f.name
        
        try:
            # Execute R script
            result = subprocess.run(
                ['Rscript', script_path],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode != 0:
                raise RuntimeError(f"R script execution failed: {result.stderr}")
            
            # Read generated PNG files
            output_dir = self.r_script_dir / 'output'
            visualizations = []

            plot_types = [
                ('histogram.png', 'Histogram'),
                ('density.png', 'Density Plot'),
                ('boxplot.png', 'Box Plot'),
                ('violin.png', 'Violin Plot')
            ]

            for filename, title in plot_types:
                png_path = output_dir / filename
                if png_path.exists():
                    import base64
                    with open(png_path, 'rb') as f:
                        png_content = base64.b64encode(f.read()).decode('utf-8')

                    visualizations.append({
                        'type': title.lower().replace(' ', '_'),
                        'title': title,
                        'png': png_content
                    })

                    # Clean up PNG file
                    png_path.unlink()
            
            return visualizations
            
        finally:
            # Clean up temporary script file
            os.unlink(script_path)

    def _execute_r_script_multi_column(self, r_script: str, columns: List[str]) -> List[Dict[str, str]]:
        """Execute R script for multi-column visualizations and return SVG content"""

        # Create temporary R script file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.R', delete=False) as f:
            f.write(r_script)
            script_path = f.name

        try:
            # Execute R script
            result = subprocess.run(
                ['Rscript', script_path],
                capture_output=True,
                text=True,
                timeout=60  # Longer timeout for multiple columns
            )

            if result.returncode != 0:
                raise RuntimeError(f"R script execution failed: {result.stderr}")

            # Read generated PNG files organized by plot type
            output_dir = self.r_script_dir / 'output'
            visualizations = []

            # Define plot types and their order
            plot_types = ['histogram', 'boxplot', 'density', 'violin']
            plot_titles = ['Histogram', 'Box Plot', 'Density Plot', 'Violin Plot']

            # Collect plots organized by type, then by column
            for plot_type, plot_title in zip(plot_types, plot_titles):
                for column in columns:
                    filename = f"{plot_type}_{column}.png"
                    png_path = output_dir / filename

                    if png_path.exists():
                        import base64
                        with open(png_path, 'rb') as f:
                            png_content = base64.b64encode(f.read()).decode('utf-8')

                        visualizations.append({
                            'type': f"{plot_type}_{column}",
                            'title': f"{plot_title} - {column}",
                            'png': png_content,
                            'column': column,
                            'plot_type': plot_type
                        })

                        # Clean up PNG file
                        png_path.unlink()

            return visualizations

        finally:
            # Clean up temporary script file
            os.unlink(script_path)
    
    def check_r_availability(self) -> bool:
        """Check if R and required packages are available"""
        try:
            result = subprocess.run(
                ['Rscript', '-e', 'library(ggplot2); library(dplyr); library(viridis); cat("OK")'],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0 and 'OK' in result.stdout
        except:
            return False

    def check_gtsummary_availability(self) -> bool:
        """Check if R and gtsummary package are available"""
        try:
            result = subprocess.run(
                ['Rscript', '-e', 'library(gtsummary); library(dplyr); library(gt); cat("OK")'],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0 and 'OK' in result.stdout
        except:
            return False

    def _is_numeric_type(self, data_type: str) -> bool:
        """Check if a data type is numeric"""
        data_type_lower = data_type.lower()
        numeric_types = [
            'int', 'integer', 'bigint', 'smallint', 'tinyint',
            'float', 'double', 'decimal', 'numeric', 'real',
            'number', 'money'
        ]
        return any(num_type in data_type_lower for num_type in numeric_types)

    def _generate_categorical_r_visualizations(self, data: List[Tuple], column: str, table: str,
                                              where_clause: Optional[str] = None,
                                              limit_clause: Optional[int] = None) -> List[Dict[str, str]]:
        """Generate R visualizations for categorical variables using ggplot2"""

        # Extract categorical values from data tuples
        values = [str(row[0]) for row in data if row[0] is not None]

        if not values:
            raise ValueError("No values found for categorical visualization")

        # Create R script for categorical visualizations
        r_script = self._create_categorical_r_script(values, column, table, where_clause, limit_clause)

        # Execute R script and get SVG outputs
        return self._execute_categorical_r_script(r_script)

    def _create_categorical_r_script(self, values: List[str], column: str, table: str,
                                    where_clause: Optional[str] = None,
                                    limit_clause: Optional[int] = None) -> str:
        """Create R script for generating categorical visualizations"""

        # Create subtitle with filter information
        subtitle_parts = [f"from {table}"]
        if where_clause:
            where_display = where_clause if len(where_clause) <= 40 else where_clause[:37] + "..."
            subtitle_parts.append(f"WHERE {where_display}")
        if limit_clause:
            subtitle_parts.append(f"LIMIT {limit_clause}")

        subtitle_text = " | ".join(subtitle_parts)
        # Calculate actual data points after potential LIMIT
        actual_data_points = min(len(values), limit_clause) if limit_clause else len(values)
        data_points_text = f"({actual_data_points} data points)"

        # Prepare data for R (escape quotes and handle special characters)
        r_values = ', '.join([f'"{str(val).replace(chr(34), chr(92)+chr(34))}"' for val in values])

        r_script = f'''
# Load required libraries
library(ggplot2)
library(dplyr)
library(viridis)

# Create data frame
data <- data.frame({column} = c({r_values}), stringsAsFactors = FALSE)

# Apply LIMIT constraint in R if specified
{f"data <- data %>% head({limit_clause})" if limit_clause else "# No LIMIT constraint"}

# Create frequency table
freq_data <- data %>%
    count({column}, sort = TRUE) %>%
    mutate(percentage = n / sum(n) * 100,
           label = paste0({column}, "\\n(", n, ", ", round(percentage, 1), "%)"))

# Set up PNG output directory
output_dir <- "r_scripts/output"
dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)

# Function to save plot as PNG
save_png <- function(plot, filename, width = 800, height = 600, dpi = 150) {{
    png(file.path(output_dir, filename), width = width, height = height, res = dpi)
    print(plot)
    dev.off()
}}

# Create subtitle with filter information
full_subtitle <- paste("{column}", "{subtitle_text}", "{data_points_text}")

# Define consistent color palette for all categorical plots
# This ensures the same category has the same color across bar, pie, and doughnut charts
color_palette <- scale_fill_brewer(type = "qual", palette = "Set3")

# 1. Bar Chart (ggplot2)
p1 <- ggplot(freq_data, aes(x = reorder({column}, -n), y = n, fill = {column})) +
    geom_col(alpha = 0.8, color = "white", size = 0.5) +
    geom_text(aes(label = n), vjust = -0.3, size = 3.5, fontface = "bold") +
    labs(title = "Bar Chart",
         subtitle = full_subtitle,
         x = "{column}", y = "Count") +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 10),
          axis.text.x = element_text(angle = 45, hjust = 1),
          legend.position = "none",
          panel.grid.minor = element_blank(),
          panel.grid.major.x = element_blank()) +
    color_palette

save_png(p1, "barchart.png")

# 2. Pie Chart (ggplot2)
p2 <- ggplot(freq_data, aes(x = "", y = n, fill = {column})) +
    geom_col(width = 1, color = "white", size = 1) +
    coord_polar("y", start = 0) +
    geom_text(aes(label = paste0(round(percentage, 1), "%")),
              position = position_stack(vjust = 0.5), size = 3.5, fontface = "bold", color = "white") +
    labs(title = "Pie Chart",
         subtitle = full_subtitle,
         fill = "{column}") +
    theme_void() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 10),
          legend.position = "bottom",
          legend.title = element_text(face = "bold")) +
    color_palette

save_png(p2, "piechart.png")

# 3. Doughnut Chart (ggplot2)
p3 <- ggplot(freq_data, aes(x = 2, y = n, fill = {column})) +
    geom_col(width = 1, color = "white", size = 1) +
    coord_polar("y", start = 0) +
    xlim(0.5, 2.5) +
    geom_text(aes(label = paste0(round(percentage, 1), "%")),
              position = position_stack(vjust = 0.5), size = 3.5, fontface = "bold", color = "white") +
    labs(title = "Doughnut Chart",
         subtitle = full_subtitle,
         fill = "{column}") +
    theme_void() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 10),
          legend.position = "bottom",
          legend.title = element_text(face = "bold")) +
    color_palette

save_png(p3, "doughnut.png")

# Output completion message
cat("Categorical visualizations generated successfully\\n")
'''

        return r_script

    def _execute_categorical_r_script(self, r_script: str) -> List[Dict[str, str]]:
        """Execute R script for categorical visualizations and return SVG content"""

        # Create temporary R script file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.R', delete=False) as f:
            f.write(r_script)
            script_path = f.name

        try:
            # Execute R script
            result = subprocess.run(
                ['Rscript', script_path],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                raise RuntimeError(f"R script execution failed: {result.stderr}")

            # Read generated PNG files
            output_dir = self.r_script_dir / 'output'
            visualizations = []

            plot_types = [
                ('barchart.png', 'Bar Chart'),
                ('piechart.png', 'Pie Chart'),
                ('doughnut.png', 'Doughnut Chart')
            ]

            for filename, title in plot_types:
                png_path = output_dir / filename
                if png_path.exists():
                    import base64
                    with open(png_path, 'rb') as f:
                        png_content = base64.b64encode(f.read()).decode('utf-8')

                    visualizations.append({
                        'type': title.lower().replace(' ', '_'),
                        'title': title,
                        'png': png_content
                    })

                    # Clean up PNG file
                    png_path.unlink()

            return visualizations

        finally:
            # Clean up temporary script file
            os.unlink(script_path)

    def _generate_mixed_column_r_visualizations(self, data: List[Tuple], all_columns: List[str],
                                               numeric_columns: List[str], categorical_columns: List[str],
                                               table: str, where_clause: Optional[str] = None,
                                               limit_clause: Optional[int] = None) -> List[Dict[str, str]]:
        """Generate R visualizations for mixed numeric and categorical columns"""

        visualizations = []

        # Generate numeric visualizations
        if numeric_columns:
            numeric_data = {}
            for i, column in enumerate(all_columns):
                if column in numeric_columns:
                    values = [float(row[i]) for row in data if row[i] is not None and str(row[i]).replace('.', '').replace('-', '').isdigit()]
                    if values:
                        numeric_data[column] = values

            if numeric_data:
                r_script = self._create_multi_column_r_script(numeric_data, table, where_clause, limit_clause)
                numeric_visualizations = self._execute_r_script_multi_column(r_script, numeric_columns)
                visualizations.extend(numeric_visualizations)

        # Generate categorical visualizations
        if categorical_columns:
            for i, column in enumerate(all_columns):
                if column in categorical_columns:
                    values = [str(row[i]) for row in data if row[i] is not None]
                    if values:
                        r_script = self._create_categorical_r_script(values, column, table, where_clause, limit_clause)
                        categorical_visualizations = self._execute_categorical_r_script(r_script)

                        # Add column information to categorical visualizations
                        for viz in categorical_visualizations:
                            viz['column'] = column
                            viz['plot_type'] = viz['type']
                            viz['type'] = f"{viz['type']}_{column}"
                            viz['title'] = f"{viz['title']} - {column}"

                        visualizations.extend(categorical_visualizations)

        return visualizations

    def _execute_scatterplot(self, params: Dict[str, str], start_time: float) -> Dict[str, Any]:
        """Execute SCATTERPLOT command"""
        x_column = params['x_column']
        y_column = params['y_column']
        table = params['table']
        where_clause = params.get('where')
        limit_clause = params.get('limit')

        try:
            # Validate table and columns exist
            table_info = self.db_manager.get_table_info(table)
            if not table_info:
                raise ValueError(f"Table '{table}' not found")

            # Check if columns exist and get actual names
            column_names = [col['name'] for col in table_info['columns']]
            column_names_lower = [name.lower() for name in column_names]

            # Find actual column names (case-insensitive)
            actual_x_column = None
            actual_y_column = None

            for i, col_lower in enumerate(column_names_lower):
                if col_lower == x_column.lower():
                    actual_x_column = column_names[i]
                if col_lower == y_column.lower():
                    actual_y_column = column_names[i]

            if actual_x_column is None:
                available_columns = ', '.join(column_names)
                raise ValueError(f"Column '{x_column}' not found in table '{table}'. Available columns: {available_columns}")

            if actual_y_column is None:
                available_columns = ', '.join(column_names)
                raise ValueError(f"Column '{y_column}' not found in table '{table}'. Available columns: {available_columns}")

            # Build data query
            data_query = f"SELECT {actual_x_column}, {actual_y_column} FROM {table} WHERE {actual_x_column} IS NOT NULL AND {actual_y_column} IS NOT NULL"

            if where_clause:
                data_query += f" AND ({where_clause})"

            if limit_clause:
                data_query += f" LIMIT {limit_clause}"

            # Execute query
            data_result = self.db_manager.execute_query(data_query)

            if not data_result['success']:
                raise ValueError(f"Failed to fetch data: {data_result['error']}")

            if len(data_result['data']) == 0:
                filter_info = ""
                if where_clause:
                    filter_info += f" with WHERE condition: {where_clause}"
                if limit_clause:
                    filter_info += f" with LIMIT: {limit_clause}"
                raise ValueError(f"No data found for columns '{actual_x_column}', '{actual_y_column}' in table '{table}'{filter_info}")

            # Generate scatterplot
            visualizations = self._generate_scatterplot_r_visualization(
                data_result['data'], actual_x_column, actual_y_column, table, where_clause, limit_clause
            )

            execution_time = round(time.time() - start_time, 3)

            result = {
                'success': True,
                'custom_command': 'SCATTERPLOT',
                'visualizations': visualizations,
                'x_column': actual_x_column,
                'y_column': actual_y_column,
                'table': table,
                'data_points': len(data_result['data']),
                'execution_time': execution_time,
                'query_used': data_query
            }

            if where_clause:
                result['where_clause'] = where_clause
            if limit_clause:
                result['limit_clause'] = limit_clause

            return result

        except Exception as e:
            execution_time = round(time.time() - start_time, 3)
            return {
                'success': False,
                'error': str(e),
                'execution_time': execution_time
            }

    def _generate_scatterplot_r_visualization(self, data: List[Tuple], x_column: str, y_column: str,
                                             table: str, where_clause: Optional[str] = None,
                                             limit_clause: Optional[int] = None) -> List[Dict[str, str]]:
        """Generate R scatterplot visualization using ggplot2"""

        # Extract x and y values from data tuples
        x_values = []
        y_values = []

        for row in data:
            if row[0] is not None and row[1] is not None:
                try:
                    x_val = float(row[0])
                    y_val = float(row[1])
                    x_values.append(x_val)
                    y_values.append(y_val)
                except (ValueError, TypeError):
                    continue

        if not x_values or not y_values:
            raise ValueError("No numeric values found for scatterplot visualization")

        # Create R script for scatterplot
        r_script = self._create_scatterplot_r_script(x_values, y_values, x_column, y_column,
                                                    table, where_clause, limit_clause)

        # Execute R script and get PNG output
        return self._execute_scatterplot_r_script(r_script)

    def _create_scatterplot_r_script(self, x_values: List[float], y_values: List[float],
                                    x_column: str, y_column: str, table: str,
                                    where_clause: Optional[str] = None,
                                    limit_clause: Optional[int] = None) -> str:
        """Create R script for generating scatterplot"""

        # Create subtitle with filter information
        subtitle_parts = [f"from {table}"]
        if where_clause:
            where_display = where_clause if len(where_clause) <= 40 else where_clause[:37] + "..."
            subtitle_parts.append(f"WHERE {where_display}")
        if limit_clause:
            subtitle_parts.append(f"LIMIT {limit_clause}")

        subtitle_text = " | ".join(subtitle_parts)
        # Calculate actual data points after potential LIMIT
        actual_data_points = min(len(x_values), limit_clause) if limit_clause else len(x_values)
        data_points_text = f"({actual_data_points} data points)"

        # Prepare data for R
        r_x_values = ', '.join(map(str, x_values))
        r_y_values = ', '.join(map(str, y_values))

        r_script = f'''
# Load required libraries
library(ggplot2)
library(dplyr)
library(viridis)

# Create data frame
data <- data.frame(
    {x_column} = c({r_x_values}),
    {y_column} = c({r_y_values})
)

# Apply LIMIT constraint in R if specified
{f"data <- data %>% head({limit_clause})" if limit_clause else "# No LIMIT constraint"}

# Set up PNG output directory
output_dir <- "r_scripts/output"
dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)

# Function to save plot as PNG
save_png <- function(plot, filename, width = 800, height = 600, dpi = 150) {{
    png(file.path(output_dir, filename), width = width, height = height, res = dpi)
    print(plot)
    dev.off()
}}

# Create subtitle
full_subtitle <- paste("{x_column} vs {y_column}", "{subtitle_text}", "{data_points_text}")

# Create scatterplot (ggplot2)
p1 <- ggplot(data, aes(x = {x_column}, y = {y_column})) +
    geom_point(alpha = 0.7, size = 2, color = "#3498db") +
    geom_smooth(method = "lm", se = TRUE, color = "#e74c3c", size = 1) +
    labs(title = "Scatterplot",
         subtitle = full_subtitle,
         x = "{x_column}", y = "{y_column}") +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 10),
          panel.grid.minor = element_blank())

save_png(p1, "scatterplot.png")

# Output completion message
cat("Scatterplot visualization generated successfully\\n")
'''

        return r_script

    def _execute_scatterplot_r_script(self, r_script: str) -> List[Dict[str, str]]:
        """Execute R script for scatterplot and return PNG content"""

        # Create temporary R script file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.R', delete=False) as f:
            f.write(r_script)
            script_path = f.name

        try:
            # Execute R script
            result = subprocess.run(
                ['Rscript', script_path],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                raise RuntimeError(f"R script execution failed: {result.stderr}")

            # Read generated PNG file
            output_dir = self.r_script_dir / 'output'
            visualizations = []

            png_path = output_dir / "scatterplot.png"
            if png_path.exists():
                import base64
                with open(png_path, 'rb') as f:
                    png_content = base64.b64encode(f.read()).decode('utf-8')

                visualizations.append({
                    'type': 'scatterplot',
                    'title': 'Scatterplot',
                    'png': png_content
                })

                # Clean up PNG file
                png_path.unlink()

            return visualizations

        finally:
            # Clean up temporary script file
            os.unlink(script_path)

    def _execute_grouped_boxplot(self, params: Dict[str, str], start_time: float) -> Dict[str, Any]:
        """Execute GROUPED_BOXPLOT command"""
        numeric_column = params['numeric_column']
        group_column = params['group_column']
        table = params['table']
        where_clause = params.get('where')
        limit_clause = params.get('limit')

        try:
            # Validate table and columns exist
            table_info = self.db_manager.get_table_info(table)
            if not table_info:
                raise ValueError(f"Table '{table}' not found")

            # Check if columns exist and get actual names
            column_names = [col['name'] for col in table_info['columns']]
            column_names_lower = [name.lower() for name in column_names]

            # Find actual column names (case-insensitive)
            actual_numeric_column = None
            actual_group_column = None

            for i, col_lower in enumerate(column_names_lower):
                if col_lower == numeric_column.lower():
                    actual_numeric_column = column_names[i]
                if col_lower == group_column.lower():
                    actual_group_column = column_names[i]

            if actual_numeric_column is None:
                available_columns = ', '.join(column_names)
                raise ValueError(f"Column '{numeric_column}' not found in table '{table}'. Available columns: {available_columns}")

            if actual_group_column is None:
                available_columns = ', '.join(column_names)
                raise ValueError(f"Column '{group_column}' not found in table '{table}'. Available columns: {available_columns}")

            # Build data query
            data_query = f"SELECT {actual_numeric_column}, {actual_group_column} FROM {table} WHERE {actual_numeric_column} IS NOT NULL AND {actual_group_column} IS NOT NULL"

            if where_clause:
                data_query += f" AND ({where_clause})"

            if limit_clause:
                data_query += f" LIMIT {limit_clause}"

            # Execute query
            data_result = self.db_manager.execute_query(data_query)

            if not data_result['success']:
                raise ValueError(f"Failed to fetch data: {data_result['error']}")

            if len(data_result['data']) == 0:
                filter_info = ""
                if where_clause:
                    filter_info += f" with WHERE condition: {where_clause}"
                if limit_clause:
                    filter_info += f" with LIMIT: {limit_clause}"
                raise ValueError(f"No data found for columns '{actual_numeric_column}', '{actual_group_column}' in table '{table}'{filter_info}")

            # Generate grouped boxplot
            visualizations = self._generate_grouped_boxplot_r_visualization(
                data_result['data'], actual_numeric_column, actual_group_column, table, where_clause, limit_clause
            )

            execution_time = round(time.time() - start_time, 3)

            result = {
                'success': True,
                'custom_command': 'GROUPED_BOXPLOT',
                'visualizations': visualizations,
                'numeric_column': actual_numeric_column,
                'group_column': actual_group_column,
                'table': table,
                'data_points': len(data_result['data']),
                'execution_time': execution_time,
                'query_used': data_query
            }

            if where_clause:
                result['where_clause'] = where_clause
            if limit_clause:
                result['limit_clause'] = limit_clause

            return result

        except Exception as e:
            execution_time = round(time.time() - start_time, 3)
            return {
                'success': False,
                'error': str(e),
                'execution_time': execution_time
            }

    def _generate_grouped_boxplot_r_visualization(self, data: List[Tuple], numeric_column: str,
                                                 group_column: str, table: str,
                                                 where_clause: Optional[str] = None,
                                                 limit_clause: Optional[int] = None) -> List[Dict[str, str]]:
        """Generate R grouped boxplot visualization using ggplot2"""

        # Extract numeric and group values from data tuples
        numeric_values = []
        group_values = []

        for row in data:
            if row[0] is not None and row[1] is not None:
                try:
                    numeric_val = float(row[0])
                    group_val = str(row[1])
                    numeric_values.append(numeric_val)
                    group_values.append(group_val)
                except (ValueError, TypeError):
                    continue

        if not numeric_values or not group_values:
            raise ValueError("No valid values found for grouped boxplot visualization")

        # Create R script for grouped boxplot
        r_script = self._create_grouped_boxplot_r_script(numeric_values, group_values,
                                                        numeric_column, group_column,
                                                        table, where_clause, limit_clause)

        # Execute R script and get PNG output
        return self._execute_grouped_boxplot_r_script(r_script)

    def _create_grouped_boxplot_r_script(self, numeric_values: List[float], group_values: List[str],
                                        numeric_column: str, group_column: str, table: str,
                                        where_clause: Optional[str] = None,
                                        limit_clause: Optional[int] = None) -> str:
        """Create R script for generating grouped boxplot"""

        # Create subtitle with filter information
        subtitle_parts = [f"from {table}"]
        if where_clause:
            where_display = where_clause if len(where_clause) <= 40 else where_clause[:37] + "..."
            subtitle_parts.append(f"WHERE {where_display}")
        if limit_clause:
            subtitle_parts.append(f"LIMIT {limit_clause}")

        subtitle_text = " | ".join(subtitle_parts)
        # Calculate actual data points after potential LIMIT
        actual_data_points = min(len(numeric_values), limit_clause) if limit_clause else len(numeric_values)
        data_points_text = f"({actual_data_points} data points)"

        # Prepare data for R (escape quotes in group values)
        r_numeric_values = ', '.join(map(str, numeric_values))
        r_group_values = ', '.join([f'"{str(val).replace(chr(34), chr(92)+chr(34))}"' for val in group_values])

        r_script = f'''
# Load required libraries
library(ggplot2)
library(dplyr)
library(viridis)

# Create data frame
data <- data.frame(
    {numeric_column} = c({r_numeric_values}),
    {group_column} = c({r_group_values}),
    stringsAsFactors = FALSE
)

# Apply LIMIT constraint in R if specified
{f"data <- data %>% head({limit_clause})" if limit_clause else "# No LIMIT constraint"}

# Set up PNG output directory
output_dir <- "r_scripts/output"
dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)

# Function to save plot as PNG
save_png <- function(plot, filename, width = 800, height = 600, dpi = 150) {{
    png(file.path(output_dir, filename), width = width, height = height, res = dpi)
    print(plot)
    dev.off()
}}

# Create subtitle
full_subtitle <- paste("{numeric_column} by {group_column}", "{subtitle_text}", "{data_points_text}")

# Create grouped boxplot (ggplot2)
p1 <- ggplot(data, aes(x = {group_column}, y = {numeric_column}, fill = {group_column})) +
    geom_boxplot(alpha = 0.8, outlier.color = "#e74c3c", outlier.size = 2) +
    labs(title = "Grouped Box Plot",
         subtitle = full_subtitle,
         x = "{group_column}", y = "{numeric_column}") +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 10),
          axis.text.x = element_text(angle = 45, hjust = 1),
          legend.position = "none",
          panel.grid.minor = element_blank(),
          panel.grid.major.x = element_blank()) +
    scale_fill_brewer(type = "qual", palette = "Set3")

save_png(p1, "grouped_boxplot.png")

# Output completion message
cat("Grouped boxplot visualization generated successfully\\n")
'''

        return r_script

    def _execute_grouped_boxplot_r_script(self, r_script: str) -> List[Dict[str, str]]:
        """Execute R script for grouped boxplot and return PNG content"""

        # Create temporary R script file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.R', delete=False) as f:
            f.write(r_script)
            script_path = f.name

        try:
            # Execute R script
            result = subprocess.run(
                ['Rscript', script_path],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                raise RuntimeError(f"R script execution failed: {result.stderr}")

            # Read generated PNG file
            output_dir = self.r_script_dir / 'output'
            visualizations = []

            png_path = output_dir / "grouped_boxplot.png"
            if png_path.exists():
                import base64
                with open(png_path, 'rb') as f:
                    png_content = base64.b64encode(f.read()).decode('utf-8')

                visualizations.append({
                    'type': 'grouped_boxplot',
                    'title': 'Grouped Box Plot',
                    'png': png_content
                })

                # Clean up PNG file
                png_path.unlink()

            return visualizations

        finally:
            # Clean up temporary script file
            os.unlink(script_path)

    def _execute_grouped_barchart(self, params: Dict[str, str], start_time: float) -> Dict[str, Any]:
        """Execute GROUPED_BARCHART command"""
        main_column = params['main_column']
        group_column = params['group_column']
        table = params['table']
        where_clause = params.get('where')
        limit_clause = params.get('limit')

        try:
            # Validate table and columns exist
            table_info = self.db_manager.get_table_info(table)
            if not table_info:
                raise ValueError(f"Table '{table}' not found")

            # Check if columns exist and get actual names
            column_names = [col['name'] for col in table_info['columns']]
            column_names_lower = [name.lower() for name in column_names]

            # Find actual column names (case-insensitive)
            actual_main_column = None
            actual_group_column = None

            for i, col_lower in enumerate(column_names_lower):
                if col_lower == main_column.lower():
                    actual_main_column = column_names[i]
                if col_lower == group_column.lower():
                    actual_group_column = column_names[i]

            if actual_main_column is None:
                available_columns = ', '.join(column_names)
                raise ValueError(f"Column '{main_column}' not found in table '{table}'. Available columns: {available_columns}")

            if actual_group_column is None:
                available_columns = ', '.join(column_names)
                raise ValueError(f"Column '{group_column}' not found in table '{table}'. Available columns: {available_columns}")

            # Build data query
            data_query = f"SELECT {actual_main_column}, {actual_group_column} FROM {table} WHERE {actual_main_column} IS NOT NULL AND {actual_group_column} IS NOT NULL"

            if where_clause:
                data_query += f" AND ({where_clause})"

            if limit_clause:
                data_query += f" LIMIT {limit_clause}"

            # Execute query
            data_result = self.db_manager.execute_query(data_query)

            if not data_result['success']:
                raise ValueError(f"Failed to fetch data: {data_result['error']}")

            if len(data_result['data']) == 0:
                filter_info = ""
                if where_clause:
                    filter_info += f" with WHERE condition: {where_clause}"
                if limit_clause:
                    filter_info += f" with LIMIT: {limit_clause}"
                raise ValueError(f"No data found for columns '{actual_main_column}', '{actual_group_column}' in table '{table}'{filter_info}")

            # Generate grouped barcharts
            visualizations = self._generate_grouped_barchart_r_visualization(
                data_result['data'], actual_main_column, actual_group_column, table, where_clause, limit_clause
            )

            execution_time = round(time.time() - start_time, 3)

            result = {
                'success': True,
                'custom_command': 'GROUPED_BARCHART',
                'visualizations': visualizations,
                'main_column': actual_main_column,
                'group_column': actual_group_column,
                'table': table,
                'data_points': len(data_result['data']),
                'execution_time': execution_time,
                'query_used': data_query
            }

            if where_clause:
                result['where_clause'] = where_clause
            if limit_clause:
                result['limit_clause'] = limit_clause

            return result

        except Exception as e:
            execution_time = round(time.time() - start_time, 3)
            return {
                'success': False,
                'error': str(e),
                'execution_time': execution_time
            }

    def _generate_grouped_barchart_r_visualization(self, data: List[Tuple], main_column: str,
                                                  group_column: str, table: str,
                                                  where_clause: Optional[str] = None,
                                                  limit_clause: Optional[int] = None) -> List[Dict[str, str]]:
        """Generate R grouped and stacked barchart visualizations using ggplot2"""

        # Extract main and group values from data tuples
        main_values = []
        group_values = []

        for row in data:
            if row[0] is not None and row[1] is not None:
                main_val = str(row[0])
                group_val = str(row[1])
                main_values.append(main_val)
                group_values.append(group_val)

        if not main_values or not group_values:
            raise ValueError("No valid values found for grouped barchart visualization")

        # Create R script for grouped and stacked barcharts
        r_script = self._create_grouped_barchart_r_script(main_values, group_values,
                                                         main_column, group_column,
                                                         table, where_clause, limit_clause)

        # Execute R script and get PNG outputs
        return self._execute_grouped_barchart_r_script(r_script)

    def _create_grouped_barchart_r_script(self, main_values: List[str], group_values: List[str],
                                         main_column: str, group_column: str, table: str,
                                         where_clause: Optional[str] = None,
                                         limit_clause: Optional[int] = None) -> str:
        """Create R script for generating grouped and stacked barcharts"""

        # Create subtitle with filter information
        subtitle_parts = [f"from {table}"]
        if where_clause:
            where_display = where_clause if len(where_clause) <= 40 else where_clause[:37] + "..."
            subtitle_parts.append(f"WHERE {where_display}")
        if limit_clause:
            subtitle_parts.append(f"LIMIT {limit_clause}")

        subtitle_text = " | ".join(subtitle_parts)
        # Calculate actual data points after potential LIMIT
        actual_data_points = min(len(main_values), limit_clause) if limit_clause else len(main_values)
        data_points_text = f"({actual_data_points} data points)"

        # Prepare data for R (escape quotes)
        r_main_values = ', '.join([f'"{str(val).replace(chr(34), chr(92)+chr(34))}"' for val in main_values])
        r_group_values = ', '.join([f'"{str(val).replace(chr(34), chr(92)+chr(34))}"' for val in group_values])

        r_script = f'''
# Load required libraries
library(ggplot2)
library(dplyr)
library(viridis)

# Create data frame
data <- data.frame(
    {main_column} = c({r_main_values}),
    {group_column} = c({r_group_values}),
    stringsAsFactors = FALSE
)

# Apply LIMIT constraint in R if specified
{f"data <- data %>% head({limit_clause})" if limit_clause else "# No LIMIT constraint"}

# Create frequency table for grouped analysis
freq_data <- data %>%
    count({main_column}, {group_column}) %>%
    group_by({main_column}) %>%
    mutate(percentage = n / sum(n) * 100)

# Set up PNG output directory
output_dir <- "r_scripts/output"
dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)

# Function to save plot as PNG
save_png <- function(plot, filename, width = 800, height = 600, dpi = 150) {{
    png(file.path(output_dir, filename), width = width, height = height, res = dpi)
    print(plot)
    dev.off()
}}

# Create subtitle
full_subtitle <- paste("{main_column} by {group_column}", "{subtitle_text}", "{data_points_text}")

# Define consistent color palette
color_palette <- scale_fill_brewer(type = "qual", palette = "Set3")

# 1. Grouped Bar Chart (ggplot2)
p1 <- ggplot(freq_data, aes(x = {main_column}, y = n, fill = {group_column})) +
    geom_col(position = "dodge", alpha = 0.8, color = "white", size = 0.5) +
    geom_text(aes(label = n), position = position_dodge(width = 0.9), vjust = -0.3, size = 3) +
    labs(title = "Grouped Bar Chart",
         subtitle = full_subtitle,
         x = "{main_column}", y = "Count", fill = "{group_column}") +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 10),
          axis.text.x = element_text(angle = 45, hjust = 1),
          legend.position = "bottom",
          panel.grid.minor = element_blank(),
          panel.grid.major.x = element_blank()) +
    color_palette

save_png(p1, "grouped_barchart.png")

# 2. Stacked Percentage Bar Chart (ggplot2)
p2 <- ggplot(freq_data, aes(x = {main_column}, y = percentage, fill = {group_column})) +
    geom_col(position = "stack", alpha = 0.8, color = "white", size = 0.5) +
    geom_text(aes(label = paste0(round(percentage, 1), "%")),
              position = position_stack(vjust = 0.5), size = 3, fontface = "bold", color = "white") +
    labs(title = "Stacked Percentage Bar Chart",
         subtitle = full_subtitle,
         x = "{main_column}", y = "Percentage (%)", fill = "{group_column}") +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
          plot.subtitle = element_text(hjust = 0.5, size = 10),
          axis.text.x = element_text(angle = 45, hjust = 1),
          legend.position = "bottom",
          panel.grid.minor = element_blank(),
          panel.grid.major.x = element_blank()) +
    color_palette +
    ylim(0, 100)

save_png(p2, "stacked_barchart.png")

# Output completion message
cat("Grouped and stacked barchart visualizations generated successfully\\n")
'''

        return r_script

    def _execute_grouped_barchart_r_script(self, r_script: str) -> List[Dict[str, str]]:
        """Execute R script for grouped barcharts and return PNG content"""

        # Create temporary R script file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.R', delete=False) as f:
            f.write(r_script)
            script_path = f.name

        try:
            # Execute R script
            result = subprocess.run(
                ['Rscript', script_path],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                raise RuntimeError(f"R script execution failed: {result.stderr}")

            # Read generated PNG files
            output_dir = self.r_script_dir / 'output'
            visualizations = []

            plot_types = [
                ('grouped_barchart.png', 'Grouped Bar Chart'),
                ('stacked_barchart.png', 'Stacked Percentage Bar Chart')
            ]

            for filename, title in plot_types:
                png_path = output_dir / filename
                if png_path.exists():
                    import base64
                    with open(png_path, 'rb') as f:
                        png_content = base64.b64encode(f.read()).decode('utf-8')

                    visualizations.append({
                        'type': title.lower().replace(' ', '_'),
                        'title': title,
                        'png': png_content
                    })

                    # Clean up PNG file
                    png_path.unlink()

            return visualizations

        finally:
            # Clean up temporary script file
            os.unlink(script_path)

    def _generate_gtsummary_table(self, data: List[Tuple], all_columns: List[str],
                                 selected_columns: List[str], group_by_column: Optional[str] = None,
                                 limit_clause: Optional[int] = None, table_name: str = "",
                                 where_clause: Optional[str] = None) -> str:
        """Generate summary table using gtsummary package"""

        # Create temporary CSV file with data
        temp_csv = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)

        try:
            # Write CSV header
            temp_csv.write(','.join(all_columns) + '\n')

            # Write data rows
            for row in data:
                # Convert None values to empty strings and handle quotes
                clean_row = []
                for value in row:
                    if value is None:
                        clean_row.append('')
                    else:
                        # Convert to string and escape quotes
                        str_value = str(value).replace('"', '""')
                        # Quote if contains comma or quotes
                        if ',' in str_value or '"' in str_value:
                            clean_row.append(f'"{str_value}"')
                        else:
                            clean_row.append(str_value)
                temp_csv.write(','.join(clean_row) + '\n')

            temp_csv.close()

            # Create R script for gtsummary
            r_script = self._create_gtsummary_r_script(
                temp_csv.name,
                selected_columns,
                group_by_column,
                limit_clause,
                table_name,
                where_clause
            )

            # Write R script to temporary file
            script_path = tempfile.NamedTemporaryFile(mode='w', suffix='.R', delete=False).name
            with open(script_path, 'w') as f:
                f.write(r_script)

            # Execute R script
            result = subprocess.run(
                ['Rscript', script_path],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                raise RuntimeError(f"R script execution failed: {result.stderr}")

            # Read generated HTML file
            output_dir = self.r_script_dir / 'output'
            html_path = output_dir / "summary_table.html"

            if html_path.exists():
                with open(html_path, 'r', encoding='utf-8') as f:
                    table_html = f.read()

                # Clean up HTML file
                html_path.unlink()

                return table_html
            else:
                raise RuntimeError("Summary table HTML file was not generated")

        finally:
            # Clean up temporary files
            try:
                os.unlink(temp_csv.name)
                os.unlink(script_path)
            except:
                pass

    def _create_gtsummary_r_script(self, csv_path: str, selected_columns: List[str],
                                  group_by_column: Optional[str] = None,
                                  limit_clause: Optional[int] = None,
                                  table_name: str = "", where_clause: Optional[str] = None) -> str:
        """Create R script for generating gtsummary table"""

        # Create output directory
        output_dir = self.r_script_dir / 'output'
        output_dir.mkdir(exist_ok=True)

        # Build column selection string
        columns_str = ', '.join([f'"{col}"' for col in selected_columns])

        # Build the R script
        r_script = f'''
# Load required libraries
library(gtsummary)
library(dplyr)
library(gt)

# Read data from CSV
df <- read.csv("{csv_path}", stringsAsFactors = FALSE)

# Apply LIMIT if specified (early in pipeline as per requirements)
'''

        if limit_clause:
            r_script += f'''
LIMIT <- {limit_clause}
df <- df |> head(LIMIT)
'''

        r_script += f'''
# Select only the required columns for summary
'''

        if group_by_column:
            r_script += f'''
# Include group by column in selection
summary_columns <- c({columns_str}, "{group_by_column}")
df_summary <- df |> select(all_of(summary_columns))
'''
        else:
            r_script += f'''
summary_columns <- c({columns_str})
df_summary <- df |> select(all_of(summary_columns))
'''

        # Create the gtsummary table
        if group_by_column:
            # Bivariate table with GROUP BY
            r_script += f'''
# Create bivariate summary table with GROUP BY
summary_table <- df_summary |>
  tbl_summary(
    include = c({columns_str}),
    by = "{group_by_column}",
    statistic = list(
      all_continuous() ~ "{{median}} ({{p25}}, {{p75}})",
      all_categorical() ~ "{{n}} ({{p}})"
    )
  ) |>
  add_overall() |>
  add_p()
'''
        else:
            # Univariate table
            r_script += f'''
# Create univariate summary table
summary_table <- df_summary |>
  tbl_summary(
    include = c({columns_str}),
    statistic = list(
      all_continuous() ~ "{{median}} ({{p25}}, {{p75}})",
      all_categorical() ~ "{{n}} ({{p}})"
    )
  )
'''

        # Add title and convert to HTML
        title_parts = [f"Summary Table for {table_name}"]
        if where_clause:
            title_parts.append(f"WHERE {where_clause}")
        if limit_clause:
            title_parts.append(f"LIMIT {limit_clause}")

        title = " | ".join(title_parts)

        r_script += f'''
# Add title and convert to HTML
summary_table <- summary_table |>
  modify_header(label ~ "**Variable**") |>
  modify_caption("**{title}**")

# Convert to HTML and save
html_output <- summary_table |> as_gt() |> as_raw_html()

# Write HTML to file
writeLines(html_output, "{output_dir}/summary_table.html")

cat("Summary table generated successfully\\n")
'''

        return r_script
