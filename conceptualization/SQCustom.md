# Custom SQL Code

- I want to add words that wil be syntax highlighted in the SQL client as part of SQL code in the eitor:
    + VISUALIZE
    + SUMMARIZE 
    + BOXPLOT
    + BARCHART
    + SCATTERPLOT
    + TABULATE 


- I want to implement custom SQL code in the SQL client.
- I like the way things are working now. I just need the new stuff implemented as addition
- - The first set of cistom codes will be for visualization and will use R and ggplot2 in the background. For reference look at ggplot2 documentation https://ggplot2.tidyverse.org/reference/index.html, also reference my github repo on visualization https://github.com/djynnius/ggplotviz
-All visualization should be created with ggplot. This includes histograms, density plots, boxplots, violin plots, barcharts, pie charts, and doughnut plots.

- Here are the ones I want implemented

### Numeric variables

```sql
-- This will create a histogram, density plot, boxplot and violin plot of the numeric variable
-- This will be displayed the div.card that contains query results. This can reactivly be replaced by the visuals
-- The visual images should be implemented as PNG so quality is retained on resizing to full screen
-- The display should be in 3 columns and as many rows as needed
VISUALIZE numeric_variable FROM table_name;
```

The query should also be able to hangle edge cases like

```SQL
VISUALIZE numeric_variable FROM table_name WHERE var_1 = 'value' AND var_2 < 45;
```

OR 

```SQL
VISUALIZE numeric_variable FROM table_name LIMIT 100;
```

OR 

```SQL
VISUALIZE numeric_variable FROM table_name WHERE var_1 = 'value' AND var_2 < 45 LIMIT 100;
```


Also I want to be able to implement multiple numeric variables with the plots for all of them 
example query 

```SQL
VISUALIZE numeirc_var_1, numeric_var_2 FROM table_name;
```
OR
```SQL
VISUALIZE numeirc_var_1, numeric_var_2, numeric_var_n FROM table_name;
```

The resulting visuals should be separate histograms for each numeric variable, folowed by boxplots for each numeric variable , followed by density plots and violin plots 

When deaing with only 1 variable use 3 colums for display not single column.

### Categorical variables 

```sql
-- This will create a barchart, pie chart, and doughnut plot of the categorical variable
-- This will be displayed the div.card that contains query results. This can reactivly be replaced by the visuals
-- The visual images should be implemented as PNG so quality is retained on resizing to full screen
-- The display should be in 3 columns and as many rows as needed
VISUALIZE categorical_or_binary_variable FROM table_name;
```

The query should also be able to hangle edge cases like

```SQL
VISUALIZE categorical_or_binary_variable FROM table_name WHERE var_1 = 'value' AND var_2 < 45;
```

OR 

```SQL
VISUALIZE categorical_or_binary_variable FROM table_name LIMIT 100;
```

OR 

```SQL
VISUALIZE categorical_or_binary_variable FROM table_name WHERE var_1 = 'value' AND var_2 < 45 LIMIT 100;
```


Also I want to be able to implement multiple numeric variables with the plots for all of them 
example query 

```SQL
VISUALIZE categorical_or_binary_variable_1, categorical_or_binary_variable_2 FROM table_name;
```
OR
```SQL
VISUALIZE categorical_or_binary_variable_1, categorical_or_binary_variable_2, categorical_or_binary_variable_n FROM table_name;
```

The resulting visuals should be separate barchart for each categorical variable, folowed by piechart for each categorical variable , followed by dougnut plot 

When deaing with only 1 variable use 3 colums for display not single column.

- When using LIMIT clause in any context start with limiting the dataframe to the number in the limit clause before proceding to visualize numeric or categorical variables.

```R
# SQL SELECT * FROM df LIMIT 100

LIMIT = 100

df |> 
    head(LIMIT) |> 
    ...
```

### Bivariate visualization

```sql
-- This will create a scatterplot of the two numeric variables
-- This will be displayed the div.card that contains query results. This can reactivly be replaced by the visuals
-- The visual images should be implemented as PNG so quality is retained on resizing to full screen
-- Add a regression line using the lm methodin geom_smooth() 
VISUALIZE SCATTERPLOT(numeric_variable_1, numeric_variable_2) FROM table_name;
```

```sql
-- This will create a grouped boxplot of the numeric variables, at each level of the categorical variable 
-- This will be displayed the div.card that contains query results. This can reactivly be replaced by the visuals
-- The visual images should be implemented as PNG so quality is retained on resizing to full screen
VISUALIZE  BOXPLOT(numeric_variable) FROM table_name GROUP BY categorical_variable;
```

```sql
-- This will create a grouped barchart of the the first categorical variable by the second mentioned in the group by clause, a percentage barcart of the first variable stacked by the second variable adding up to 100% for each bar 
-- This will be displayed the div.card that contains query results. This can reactivly be replaced by the visuals
-- The visual images should be implemented as PNG so quality is retained on resizing to full screen
-- The display should be in 2 columns
VISUALIZE  BARCHART(categorical_variable_1) FROM table_name GROUP BY categorical_variable_2;
```

Remember to deal with corner and edge cases like those with LIMIT and WHERE clauses


## TABLES

- These queries will use the gtsummary package

```sql
-- This will create a table with the summary statistics of variables
-- Numeric variables will return median and (Q1, Q3) while categorical variables will return frequency counts and percentages (percent)
-- The table will be created by gtsummary tbl_summary function and will include the variables in the select statement 
-- The structure may be something like df |> tbl_summary(include = c(var_1, var_2, var_n) ,statistc = list(all_continuous() ~ '{median} ({p25}, {p75})', all_categorical() ~ '{n} ({p}%)'), missing='no')
--WHERE clauses and LIMIT clauses will also be respected 
-- This will be displayed the div.card that contains query results. This can reactivly be replaced by the table 
TABULATE var_1, var_2, var_n FROM table_name;
```

For edge cases where GROUP BY clause is used then do a bivariate table with the statistics for each group with p-values where possible.

```SQL
TABULATE var_1, var_2, var_n FROM table_name GROUP BY var_x;
-- this would be interpreted as 
-- df |> tbl_summary(include = c(var_1, var_2, var_n) ,statistc = list(all_continuous() ~ '{median} ({p25}, {p75})', all_categorical() ~ '{n} ({p}%)'), by=var_x, missing='no') |> add_overall() |> add_p() 
```
